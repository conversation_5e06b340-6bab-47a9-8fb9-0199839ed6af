#!/system/bin/sh

bootfailcount=`getprop persist.sys.boot.fail_count`

sleep 60

echo "[booting time, end wait.]"

bootcompleted=`getprop sys.start.launcher`
echo "launcher complete is $bootcompleted"
if [ "$bootcompleted" = "1" ]; then

  setprop persist.sys.boot.fail_count 0
else
  setprop persist.sys.boot.fail_count 1
fi

sleep 120

bootcompleted2=`getprop sys.boot_completed`

if [ "$bootcompleted2" = "1" ]; then
  setprop persist.sys.boot.fail_count 0
  if [ "$bootcompleted" = "0" ]; then
    echo "luancher not start..."
    reboot recovery
  fi
else
  echo "start recovery"
  reboot recovery
fi

exit
