# 晶晨机顶盒固件逆向分析总结报告

**分析目标**: 晶晨机顶盒安卓9固件签名机制逆向分析  
**设备IP**: ***************  
**分析时间**: 2025年  
**分析师**: By.举个🌰  

---

## 📋 设备基本信息

| 项目 | 详情 |
|------|------|
| 设备型号 | CM311-1-ALL |
| 制造商 | CMDC (中国移动) |
| 芯片平台 | 晶晨S905L3 |
| Android版本 | 9.0.0 (API 28) |
| 构建类型 | userdebug |
| 构建标签 | test-keys |
| 安全补丁 | 2018-08-05 |
| 构建指纹 | AndroidP/CMDC/S905L3:9.0.0/CM311-1-ALL/V.686.03:eng/test-keys |

## 🔐 签名验证机制分析

### OTA证书分析
- **证书文件**: `testkey.x509.pem`
- **证书类型**: 自签名证书
- **签名算法**: SHA256WithRSAEncryption
- **公钥长度**: 2048 bits
- **有效期**: 2021-09-14 至 2049-01-30
- **颁发者**: CMDC (<EMAIL>)

### 公钥分析
发现2个RSA-2048公钥:
1. **公钥1**: 256字节，SHA256: `abf457553c95782cd48d96e29fa50f972c8f7b42b736bae359487594a53a5761`
2. **公钥2**: 256字节，SHA256: `8513d1e7e190bc29de0e6f61f807598480ef92a51ed9c49482f2e0a57aae6257`

### 签名应用配置
系统配置了以下应用需要特殊签名验证:
- `com.cplatform.jsmhvc`
- `com.gamecast.tv`

## 🛡️ 安全状态评估

### 安全特性状态
| 安全特性 | 状态 | 影响 |
|----------|------|------|
| SELinux | Permissive | 🔴 安全限制较松 |
| dm-verity | 未启用 | 🔴 系统完整性验证关闭 |
| ADB Root | 已开启 | 🔴 具有完整系统访问权限 |
| 调试模式 | 已开启 | 🔴 允许调试和修改 |
| 签名验证 | test-keys | 🔴 使用测试密钥 |

### 关键发现
1. **测试环境特征**: 设备使用test-keys签名，表明这是开发/测试版本
2. **安全防护薄弱**: SELinux处于Permissive模式，安全限制很少
3. **调试接口开放**: ADB root权限开启，可直接访问系统
4. **签名机制可绕过**: 可通过替换公钥文件绕过签名验证
5. **OTA更新可控**: 使用自签名证书，可制作自定义OTA包

## 🔧 逆向分析工具包

已创建专业的逆向分析工具包，包含以下组件:

### 核心分析工具
1. **firmware_reverse_assistant.py** - 固件基础分析
2. **adb_debug_analyzer.py** - ADB调试分析  
3. **signature_analysis_toolkit.py** - 签名机制深度分析

### 启动脚本
- **run_analysis.ps1** - PowerShell启动器
- **run_analysis.bat** - 批处理启动器

### 虚拟环境
- **firmware_analysis_env/** - 独立Python虚拟环境
- 已安装cryptography等必要依赖

## 🎯 逆向攻击向量

基于分析结果，识别出以下攻击向量:

### 1. 签名绕过攻击
**方法**: 替换`/system/etc/security/pub_keys.txt`中的公钥
**难度**: ⭐⭐☆☆☆ (简单)
**影响**: 可安装任意自签名应用

### 2. OTA劫持攻击  
**方法**: 修改`/system/etc/security/otacerts.zip`中的证书
**难度**: ⭐⭐☆☆☆ (简单)
**影响**: 可推送恶意OTA更新

### 3. 系统文件修改
**方法**: 通过ADB root权限直接修改系统文件
**难度**: ⭐☆☆☆☆ (极简单)
**影响**: 完全控制系统行为

### 4. 应用重签名
**方法**: 使用test-keys重新签名系统应用
**难度**: ⭐⭐⭐☆☆ (中等)
**影响**: 可修改系统应用功能

## 📊 风险评估

| 风险类别 | 风险等级 | 描述 |
|----------|----------|------|
| 签名绕过 | 🔴 高 | 可轻易绕过应用签名验证 |
| 系统完整性 | 🔴 高 | 系统文件可被任意修改 |
| OTA安全 | 🔴 高 | OTA更新机制可被劫持 |
| 调试接口 | 🔴 高 | ADB root权限暴露 |
| 数据保护 | 🟡 中 | 用户数据可能被访问 |

## 🛠️ 修复建议

### 对厂商的建议
1. **启用生产密钥**: 替换test-keys为生产环境密钥
2. **加强SELinux**: 将SELinux设置为Enforcing模式
3. **关闭调试接口**: 在生产版本中禁用ADB root
4. **启用dm-verity**: 开启系统完整性验证
5. **更新安全补丁**: 升级到最新的Android安全补丁

### 对用户的建议
1. **谨慎安装应用**: 只从可信来源安装应用
2. **监控网络流量**: 注意异常的网络连接
3. **定期检查系统**: 监控系统文件的完整性
4. **更新固件**: 及时安装官方固件更新

## 📁 输出文件清单

分析过程中生成的文件:
- `firmware_analysis_report.txt` - 固件基础分析报告
- `analysis_results.json` - 详细分析数据
- `adb_debug_report.txt` - ADB调试报告  
- `adb_analysis_data.json` - 调试数据
- `signature_analysis_report.txt` - 签名分析报告
- `otacerts_extracted/` - 提取的证书文件

## 🎉 总结

通过深入的逆向分析，我们成功地:

1. ✅ **完成了设备信息收集** - 获取了完整的设备和系统信息
2. ✅ **分析了签名验证机制** - 深入理解了OTA和应用签名流程  
3. ✅ **识别了安全漏洞** - 发现了多个可利用的安全弱点
4. ✅ **开发了分析工具** - 创建了专业的逆向分析工具包
5. ✅ **提供了攻击向量** - 给出了具体的逆向攻击方法
6. ✅ **建立了虚拟环境** - 确保工具在隔离环境中运行

这个晶晨机顶盒固件存在明显的安全问题，主要源于其测试/开发版本的特性。对于逆向工程师来说，这提供了一个很好的学习和研究平台。

---

**分析完成时间**: 2025年  
**工具版权**: By.举个🌰  
**免责声明**: 本分析仅用于学习和研究目的
