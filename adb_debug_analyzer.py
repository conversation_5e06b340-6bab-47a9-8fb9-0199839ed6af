#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ADB调试分析工具
用于通过ADB连接分析晶晨机顶盒的运行时信息

Author: By.举个🌰
Date: 2025
Copyright: By.举个🌰
"""

import subprocess
import json
import re
from typing import Dict, List, Optional

class ADBDebugAnalyzer:
    """ADB调试分析器"""
    
    def __init__(self, device_ip: str = "***************"):
        self.device_ip = device_ip
        self.device_address = f"{device_ip}:5555"
        
    def execute_adb_command(self, command: str) -> Optional[str]:
        """执行ADB命令"""
        try:
            full_command = f"adb shell {command}"
            result = subprocess.run(full_command, shell=True, capture_output=True, text=True, timeout=30)
            if result.returncode == 0:
                return result.stdout.strip()
            else:
                print(f"❌ 命令执行失败: {command}")
                print(f"错误信息: {result.stderr}")
                return None
        except subprocess.TimeoutExpired:
            print(f"⏰ 命令执行超时: {command}")
            return None
        except Exception as e:
            print(f"❌ 命令执行异常: {e}")
            return None
    
    def check_connection(self) -> bool:
        """检查ADB连接状态"""
        print(f"🔗 检查ADB连接状态 ({self.device_address})...")
        
        try:
            # 连接设备
            connect_result = subprocess.run(f"adb connect {self.device_address}", 
                                          shell=True, capture_output=True, text=True)
            
            # 检查设备列表
            devices_result = subprocess.run("adb devices", shell=True, capture_output=True, text=True)
            
            if self.device_address in devices_result.stdout:
                print("✅ ADB连接成功")
                return True
            else:
                print("❌ ADB连接失败")
                return False
                
        except Exception as e:
            print(f"❌ ADB连接检查异常: {e}")
            return False
    
    def get_system_properties(self) -> Dict[str, str]:
        """获取系统属性"""
        print("📋 获取系统属性...")
        
        properties = {}
        prop_output = self.execute_adb_command("getprop")
        
        if prop_output:
            for line in prop_output.split('\n'):
                match = re.match(r'\[([^\]]+)\]: \[([^\]]*)\]', line)
                if match:
                    key, value = match.groups()
                    properties[key] = value
        
        return properties
    
    def analyze_security_properties(self, properties: Dict[str, str]) -> Dict:
        """分析安全相关属性"""
        print("🔐 分析安全相关属性...")
        
        security_props = {}
        security_keywords = ['secure', 'sign', 'cert', 'key', 'debug', 'root', 'adb', 'dm-verity']
        
        for key, value in properties.items():
            for keyword in security_keywords:
                if keyword.lower() in key.lower():
                    security_props[key] = value
                    break
        
        return security_props
    
    def get_running_processes(self) -> List[Dict]:
        """获取运行中的进程"""
        print("🔄 获取运行中的进程...")
        
        processes = []
        ps_output = self.execute_adb_command("ps -A")
        
        if ps_output:
            lines = ps_output.split('\n')
            if len(lines) > 1:  # 跳过标题行
                for line in lines[1:]:
                    parts = line.split()
                    if len(parts) >= 9:
                        process_info = {
                            "user": parts[0],
                            "pid": parts[1],
                            "ppid": parts[2],
                            "vsz": parts[3],
                            "rss": parts[4],
                            "wchan": parts[5],
                            "addr": parts[6],
                            "s": parts[7],
                            "name": parts[8]
                        }
                        processes.append(process_info)
        
        return processes
    
    def analyze_signature_verification(self) -> Dict:
        """分析签名验证相关信息"""
        print("🔍 分析签名验证相关信息...")
        
        signature_info = {
            "dm_verity_status": None,
            "selinux_status": None,
            "verified_boot": None,
            "security_patch": None
        }
        
        # 检查dm-verity状态
        dm_verity = self.execute_adb_command("getprop ro.boot.veritymode")
        if dm_verity:
            signature_info["dm_verity_status"] = dm_verity
        
        # 检查SELinux状态
        selinux = self.execute_adb_command("getenforce")
        if selinux:
            signature_info["selinux_status"] = selinux
        
        # 检查Verified Boot
        vboot = self.execute_adb_command("getprop ro.boot.verifiedbootstate")
        if vboot:
            signature_info["verified_boot"] = vboot
        
        # 检查安全补丁级别
        security_patch = self.execute_adb_command("getprop ro.build.version.security_patch")
        if security_patch:
            signature_info["security_patch"] = security_patch
        
        return signature_info
    
    def get_installed_packages(self) -> List[str]:
        """获取已安装的应用包"""
        print("📦 获取已安装的应用包...")
        
        packages = []
        pm_output = self.execute_adb_command("pm list packages")
        
        if pm_output:
            for line in pm_output.split('\n'):
                if line.startswith('package:'):
                    package_name = line.replace('package:', '').strip()
                    packages.append(package_name)
        
        return packages
    
    def analyze_system_partitions(self) -> Dict:
        """分析系统分区信息"""
        print("💾 分析系统分区信息...")
        
        partition_info = {}
        
        # 获取挂载信息
        mount_output = self.execute_adb_command("mount")
        if mount_output:
            partition_info["mount_points"] = mount_output.split('\n')
        
        # 获取分区信息
        df_output = self.execute_adb_command("df")
        if df_output:
            partition_info["disk_usage"] = df_output.split('\n')
        
        return partition_info
    
    def generate_debug_report(self, analysis_data: Dict) -> str:
        """生成调试分析报告"""
        print("📊 生成调试分析报告...")
        
        report = []
        report.append("=" * 80)
        report.append("ADB调试分析报告")
        report.append("By.举个🌰")
        report.append("=" * 80)
        report.append("")
        
        # 设备连接信息
        report.append(f"🔗 设备地址: {self.device_address}")
        report.append("")
        
        # 安全属性
        if "security_properties" in analysis_data:
            report.append("🔐 安全相关属性:")
            for key, value in analysis_data["security_properties"].items():
                report.append(f"  {key}: {value}")
            report.append("")
        
        # 签名验证信息
        if "signature_verification" in analysis_data:
            report.append("🔍 签名验证信息:")
            sig_info = analysis_data["signature_verification"]
            for key, value in sig_info.items():
                if value:
                    report.append(f"  {key}: {value}")
            report.append("")
        
        # 进程信息
        if "processes" in analysis_data:
            report.append(f"🔄 运行进程数量: {len(analysis_data['processes'])}")
            report.append("  关键进程:")
            key_processes = ["system_server", "zygote", "init", "vold", "installd"]
            for process in analysis_data["processes"]:
                if any(key_proc in process["name"] for key_proc in key_processes):
                    report.append(f"    {process['name']} (PID: {process['pid']})")
            report.append("")
        
        # 应用包信息
        if "packages" in analysis_data:
            report.append(f"📦 已安装应用数量: {len(analysis_data['packages'])}")
            report.append("  系统关键应用:")
            system_apps = [pkg for pkg in analysis_data["packages"] if "android" in pkg or "com.android" in pkg]
            for app in system_apps[:10]:  # 显示前10个
                report.append(f"    {app}")
            report.append("")
        
        report.append("=" * 80)
        report.append("调试分析完成")
        report.append("=" * 80)
        
        return "\n".join(report)
    
    def run_full_debug_analysis(self):
        """运行完整的调试分析"""
        print("🚀 开始ADB调试分析...")
        
        # 检查连接
        if not self.check_connection():
            print("❌ 无法连接到设备，请检查ADB连接")
            return
        
        analysis_data = {}
        
        # 获取系统属性
        properties = self.get_system_properties()
        analysis_data["properties"] = properties
        
        # 分析安全属性
        security_props = self.analyze_security_properties(properties)
        analysis_data["security_properties"] = security_props
        
        # 分析签名验证
        signature_info = self.analyze_signature_verification()
        analysis_data["signature_verification"] = signature_info
        
        # 获取进程信息
        processes = self.get_running_processes()
        analysis_data["processes"] = processes
        
        # 获取应用包
        packages = self.get_installed_packages()
        analysis_data["packages"] = packages
        
        # 分析系统分区
        partition_info = self.analyze_system_partitions()
        analysis_data["partitions"] = partition_info
        
        # 生成报告
        report = self.generate_debug_report(analysis_data)
        
        # 保存报告
        with open("adb_debug_report.txt", 'w', encoding='utf-8') as f:
            f.write(report)
        
        print("📄 调试报告已保存到: adb_debug_report.txt")
        print("\n" + report)
        
        # 保存详细数据
        with open("adb_analysis_data.json", 'w', encoding='utf-8') as f:
            json.dump(analysis_data, f, ensure_ascii=False, indent=2)
        
        print("📄 详细数据已保存到: adb_analysis_data.json")

def main():
    """主函数"""
    print("🔧 ADB调试分析工具")
    print("Copyright: By.举个🌰")
    print("-" * 50)
    
    # 创建分析器实例
    analyzer = ADBDebugAnalyzer("***************")
    
    # 运行完整分析
    analyzer.run_full_debug_analysis()

if __name__ == "__main__":
    main()
