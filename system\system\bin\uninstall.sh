#!/system/bin/sh
#add by liupeng 20220917 for uninstall com.galaxyitv.video Application when current launcher is tv.icntv.ott
CURRENT_LAUNCHER=`getprop persist.sys.itms.epg.launcher`
LAST_LAUNCHER=`getprop persist.sys.last.epg.launcher` 
packagesFile="/data/system/packages.xml"
gitvVod="com.galaxyitv.video"
gitvLaucher="com.gitv.tv.launcher"

#last default launcher packages is tv.icntv.ott
if [ "$LAST_LAUNCHER" == "" ];then
	LAST_LAUNCHER="tv.icntv.ott"
fi

if [ "$LAST_LAUNCHER" !=  "$CURRENT_LAUNCHER" ];then
	if [ "$CURRENT_LAUNCHER" == "tv.icntv.ott"  ];then
		if [ `grep -c ${gitvVod} ${packagesFile}` -ge 1 ];then
			pm uninstall ${gitvVod}
		fi
	elif [ "$CURRENT_LAUNCHER" == "com.gitv.launcher" ];then
		if [ `grep -c ${gitvLaucher} ${packagesFile}` -ge 1 ];then
			pm uninstall ${gitvLaucher}
		fi
	fi
	#update last launcher package name
	setprop "persist.sys.last.epg.launcher" "$CURRENT_LAUNCHER"
fi
