================================================================================
晶晨机顶盒签名机制深度分析报告
By.举个🌰
================================================================================

🔐 OTA证书分析:
  证书文件: testkey.x509.pem
    主题: <Name(C=CN,ST=GuanDong,L=ShenZhen  View,O=Cmdc,OU=Cmdc,CN=Cmdc,1.2.840.113549.1.9.1=<EMAIL>)>
    颁发者: <Name(C=CN,ST=GuanDong,L=ShenZhen  View,O=Cmdc,OU=Cmdc,CN=Cmdc,1.2.840.113549.1.9.1=<EMAIL>)>
    有效期: 2021-09-14T02:43:51 至 2049-01-30T02:43:51
    签名算法: sha256WithRSAEncryption
    公钥算法: _RSAPublicKey
    公钥长度: 2048 bits

🔑 公钥分析:
  公钥 1:
    长度: 512 字符 (256 字节)
    SHA256: abf457553c95782cd48d96e29fa50f972c8f7b42b736bae359487594a53a5761

  公钥 2:
    长度: 512 字符 (256 字节)
    SHA256: 8513d1e7e190bc29de0e6f61f807598480ef92a51ed9c49482f2e0a57aae6257

  密钥类型分析:
    推测类型: RSA-2048 (推测)
    字节长度: 256
    SHA1: 71b21ee7cdbeae15bbca8c854dd1f31e1352d294

  密钥类型分析:
    推测类型: RSA-2048 (推测)
    字节长度: 256
    SHA1: 3a3e6f0bf3a8ba16d2e00793230b504cd604a90a

🔍 签名验证机制:
  配置的签名应用:
    - com.cplatform.jsmhvc
    - com.gamecast.tv

  Recovery验证:
    文件大小: 4622613 字节
    SHA256: b71894049e049d7b666954c0a9ed157745a9857f7f6a5f122c7659332a4d422d

================================================================================
🎯 关键发现:
1. 设备使用test-keys签名，表明这是开发/测试版本
2. SELinux处于Permissive模式，安全限制较松
3. ro.secure=1但ro.debuggable=1，存在调试接口
4. ADB root权限已开启，便于调试分析
5. 安全补丁级别较旧(2018-08-05)，可能存在已知漏洞

🔧 逆向建议:
1. 可以通过替换pub_keys.txt中的公钥来绕过签名验证
2. 修改otacerts.zip中的证书可以自签名OTA包
3. 利用test-keys可以重新签名系统应用
4. 通过ADB root权限可以直接修改系统文件
================================================================================