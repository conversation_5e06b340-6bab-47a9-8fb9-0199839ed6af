@echo off
echo ========================================
echo OTA签名工具启动器
echo By.举个🌰
echo ========================================
echo.

REM 检查虚拟环境是否存在
if not exist "firmware_analysis_env\Scripts\activate.bat" (
    echo 创建虚拟环境...
    python -m venv firmware_analysis_env
    echo 虚拟环境创建完成
    echo.
)

REM 激活虚拟环境
echo 激活虚拟环境...
call firmware_analysis_env\Scripts\activate.bat

REM 安装依赖
echo 检查并安装依赖...
pip install cryptography >nul 2>&1

echo.
echo 启动OTA签名工具GUI...
python ota_signature_tool.py

echo.
echo 工具已关闭
pause
