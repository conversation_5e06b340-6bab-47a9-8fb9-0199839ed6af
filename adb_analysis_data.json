{"properties": {"amlchat.status.enable": "disable", "amplayer.blur.inlevel": "10", "amplayer.blur.inperoid": "200", "amplayer.blur.outperoid": "1500", "amplayer.codec_info.mutex": "1", "amplayer.unload.inperoid": "150", "amplayer.unload.outperoid": "1000", "android.os.Build.MANUFACTURER": "CMDC", "android.os.Build.MODEL": "CM311-1e", "android.os.Build.PRODUCTIONDATE": "2024-09", "android.os.Build.SERIAL": "0041130010012580240948E533B21C98", "android.os.Build.VERSION.INCREMENTAL": "V.686.03", "camera.disable_zsl_mode": "1", "config.disable_bluetooth": "false", "config.disable_fingerprintservice": "true", "config.disable_wiredaccessoryservice": "true", "dalvik.vm.appimageformat": "lz4", "dalvik.vm.dex2oat-Xms": "64m", "dalvik.vm.dex2oat-Xmx": "512m", "dalvik.vm.dex2oat-minidebuginfo": "true", "dalvik.vm.dexopt.secondary": "true", "dalvik.vm.heapgrowthlimit": "256m", "dalvik.vm.heapmaxfree": "32m", "dalvik.vm.heapminfree": "2m", "dalvik.vm.heapsize": "384m", "dalvik.vm.heapstartsize": "50m", "dalvik.vm.heaptargetutilization": "0.8", "dalvik.vm.image-dex2oat-Xms": "64m", "dalvik.vm.image-dex2oat-Xmx": "64m", "dalvik.vm.isa.arm.features": "default", "dalvik.vm.isa.arm.variant": "cortex-a9", "dalvik.vm.lockprof.threshold": "500", "dalvik.vm.stack-trace-dir": "/data/anr", "dalvik.vm.usejit": "true", "dalvik.vm.usejitprofiles": "true", "debug.atrace.tags.enableflags": "0", "debug.force_rtl": "0", "debug.hwui.use_buffer_age": "false", "debug.sf.disable_backpressure": "1", "debug.sf.latch_unsignaled": "1", "dev.bootcomplete": "1", "dhcp.eth0.dns1": "*************", "dhcp.eth0.gateway": "*************", "dhcp6c.eth0.dns.cnt": "1", "dhcp6c.eth0.dns0": "", "dhcp6c.eth0.dns1": "fd9e:4de:8271::1", "dhcp6c.eth0.dns2": "", "dhcp6c.eth0.dns3": "", "dhcp6c.eth0.expired": "", "dhcp6c.eth0.gateway": "fe80::3647:d4ff:fecb:df97", "dhcp6c.eth0.ipaddress": "fd9e:4de:8271:0:4ae5:33ff:feb2:1c98/64", "dhcp6c.eth0.oldipaddr": "fd9e:4de:8271::b9b/64", "dhcp6c.eth0.result": "OK", "drm.service.enabled": "1", "epg.accountidentity": "", "epg.authcode": "", "epg.cmcchomeurl": "", "epg.copyrightid": "", "epg.eccode": "", "epg.eccoporationcode": "", "epg.indexurl": "", "epg.launcher.classname": "com.aspirecn.activate.activity.OTTActivity", "epg.launcher.packagename": "com.aspirecn.activate", "epg.login": "", "epg.mobile.deviceid": "", "epg.mobile.token": "", "epg.mobile.userid": "", "epg.speechchannel.bussy": "", "epg.token": "", "epg.usergroup": "", "epg.userid": "", "hls.aml.enable": "1", "hls.curl.enable": "0", "http.getc.timeout_us": "********", "hw.ddr.size": "2048", "hwservicemanager.ready": "true", "init.svc.adbd": "running", "init.svc.audioserver": "running", "init.svc.bluetooth_init": "stopped", "init.svc.bootanim": "stopped", "init.svc.cameraserver": "running", "init.svc.chservice": "running", "init.svc.console": "running", "init.svc.drm": "running", "init.svc.dumpstate-1-0": "running", "init.svc.ext_bootfail": "stopped", "init.svc.flash_recovery": "stopped", "init.svc.gatekeeperd": "running", "init.svc.hdcp_tx22": "stopped", "init.svc.hdmicecd": "running", "init.svc.health-hal-2-0": "running", "init.svc.hidl_memory": "running", "init.svc.hwservicemanager": "running", "init.svc.imageserver": "running", "init.svc.incidentd": "running", "init.svc.installd": "running", "init.svc.iptvd": "running", "init.svc.keystore": "running", "init.svc.lmkd": "running", "init.svc.load_remote": "stopped", "init.svc.logd": "running", "init.svc.logd-reinit": "stopped", "init.svc.mdnsd": "running", "init.svc.media": "running", "init.svc.mediadrm": "running", "init.svc.mediaextractor": "running", "init.svc.mediametrics": "running", "init.svc.miracast_hdcp2": "running", "init.svc.netd": "running", "init.svc.perfprofd": "running", "init.svc.remotecfg": "stopped", "init.svc.screen_control": "running", "init.svc.servicemanager": "running", "init.svc.softprobe": "running", "init.svc.statsd": "running", "init.svc.storaged": "running", "init.svc.subtitleserver": "running", "init.svc.sunniwell_setup": "stopped", "init.svc.surfaceflinger": "running", "init.svc.swRootService": "running", "init.svc.swiperf_setup": "stopped", "init.svc.system_control": "running", "init.svc.tcpdump_capture": "stopped", "init.svc.thermalcfg": "stopped", "init.svc.thermalservice": "running", "init.svc.tombstoned": "running", "init.svc.ueventd": "running", "init.svc.uninstall": "stopped", "init.svc.usbd": "stopped", "init.svc.vendor.audio-hal-2-0": "running", "init.svc.vendor.bluetooth-1-0": "running", "init.svc.vendor.camera-provider-2-4": "running", "init.svc.vendor.cec-hal-1-0": "running", "init.svc.vendor.configstore-hal": "running", "init.svc.vendor.gatekeeper-1-0": "running", "init.svc.vendor.gralloc-2-0": "running", "init.svc.vendor.hwcomposer-2-2": "running", "init.svc.vendor.ir-hal-1-0": "running", "init.svc.vendor.keymaster-3-0": "running", "init.svc.vendor.light-hal-2-0": "running", "init.svc.vendor.media.omx": "running", "init.svc.vendor.memtrack-hal-1-0": "running", "init.svc.vendor.power-hal-1-0": "running", "init.svc.vendor.thermal-hal-1-0": "running", "init.svc.vendor.usb-hal-1-0": "running", "init.svc.vendor.wifi_hal_legacy": "running", "init.svc.vndservicemanager": "running", "init.svc.vold": "running", "init.svc.wifi_preload": "stopped", "init.svc.wificond": "running", "init.svc.xiriservice": "running", "init.svc.zygote": "running", "iptv.enablebuf": "0", "iptv.probesize": "8388608", "ipv4.net.ethernet.onreach": "true", "libplayer.blur.detect.disable": "0", "libplayer.cache.amaxframes": "4000000", "libplayer.cache.backseek": "300", "libplayer.cache.bigpktnum": "4", "libplayer.cache.debug": "1", "libplayer.cache.enable": "1", "libplayer.cache.enterkeepms": "5000", "libplayer.cache.keepframe_en": "0", "libplayer.cache.maxmem": "94371840", "libplayer.cache.seekenable": "1", "libplayer.cache.smaxframes": "2000000", "libplayer.cache.vmaxframes": "2000000", "libplayer.ffmpeg.lpbufsizemax": "67108864", "libplayer.hls.check_block": "0", "libplayer.hls.start_from_top": "1", "libplayer.hls.stpos": "-1", "libplayer.livets.softdemux": "1", "libplayer.netts.recalcpts": "1", "libplayer.rtsp.lower_transp": "3", "libplayer.switch.format": "1", "libplayer.tcp.dnscache": "1", "libplayer.tcp.get_dns_type": "0", "libplayer.tcp.get_ip_priority": "0", "libplayer.tcp.timeout": "1000", "libplayer.tcp.usev6": "1", "libplayer.ts.softdemux": "1", "log.tag.WifiHAL": "D", "log.tag.stats_log": "I", "logd.logpersistd.enable": "true", "mbx.3D_Bright.enable": "false", "media.amadec.prefilltime": "50", "media.amlogicplayer.enable": "1", "media.amnuplayer.audio.delayus": "-40000", "media.amplayer.FCC_Version": "1", "media.amplayer.blurredscreen.enabled": "true", "media.amplayer.blurredscreen.oddeven": "false", "media.amplayer.buffer.oddeven": "false", "media.amplayer.bufferingreport.enabled": "true", "media.amplayer.buffertime": "6", "media.amplayer.bufing_timeout": "90", "media.amplayer.chmobilekpi": "0", "media.amplayer.delaybuffering": "3", "media.amplayer.delaybuffering.s": "2", "media.amplayer.delayprobebuffering.s": "0", "media.amplayer.disable-vcodecs": "real,divx,divx3,divx4", "media.amplayer.displast_frame": "0", "media.amplayer.displast_frame.delay": "400", "media.amplayer.divtime": "1", "media.amplayer.dropmaxtime": "10000", "media.amplayer.dropwaitxms": "2000", "media.amplayer.dsource4local": "1", "media.amplayer.dur_update": "1", "media.amplayer.enable": "true", "media.amplayer.enable-acodecs": "ac3,eac3,rm,dts,thd,aac,m4a,asf", "media.amplayer.fcc_enable": "1", "media.amplayer.force_buf_enable": "1", "media.amplayer.force_buf_exit": "0.008", "media.amplayer.force_buf_thres": "400", "media.amplayer.force_output_packet_num": "300", "media.amplayer.highlevel": "0.95", "media.amplayer.hls_notify_en": "1", "media.amplayer.lowlevel": "0.001", "media.amplayer.lpbufferlevel": "0.05", "media.amplayer.middlelevel.4k": "0.1", "media.amplayer.midlevel": "0.07", "media.amplayer.onbuffering.S": "0.3", "media.amplayer.pre_droppcm": "0", "media.amplayer.quality": "0", "media.amplayer.refmode": "0", "media.amplayer.rtp_NATtraversal": "1", "media.amplayer.seek_async": "0", "media.amplayer.seek_keep_speed": "true", "media.amplayer.seekkeyframe": "1", "media.amplayer.seekmode": "1", "media.amplayer.skipvideotype": "null", "media.amplayer.smooth_region": "7200000000", "media.amplayer.startmode": "true", "media.amplayer.support-exname": "divx,h264,avi,ts,m2ts,mkv,mp4,mpg,mpeg,rm,rmvb,wmv,ts,dat,vob,vc1", "media.amplayer.support-exname1": "mp2,mp3,ac3", "media.amplayer.thumbnail": "true", "media.amplayer.thumbnail4http": "true", "media.amplayer.truehd": "0", "media.amplayer.unload.oddeven": "false", "media.amplayer.unloadreport.enabled": "true", "media.amplayer.videolimiter": "1", "media.amplayer.widevineenable": "false", "media.amsuperplayer.defplayer": "PV_PLAYER", "media.amsuperplayer.enable": "true", "media.amsuperplayer.m4aplayer": "STAGEFRIGHT_PLAYER", "media.arm.audio.decoder": "ape,flac,dts,ac3,eac3,wma,wmapro,mp3,aac,vorbis,raac,cook,amr,pcm,adpcm,aac_latm,rm", "media.audiohal.reverb_gain": "75", "media.cmccplayer.enable": "0", "media.decoder.vfm.defmap": "decoder ppmgr deinterlace amvideo", "media.filter.heaac": "1", "media.hls.range_type": "1", "media.html5videowin.enable": "1", "media.libplayer.curlenable": "false", "media.libplayer.ipv4only": "0", "media.libplayer.modules": "vhls_mod,dash_mod,curl_mod,prhls_mod,vm_mod,bluray_mod", "media.libplayer.net.frameseek": "1", "media.libplayer.nobufferstart": "0", "media.libplayer.seek.fwdsearch": "1", "media.managerplayer.mode": "0", "media.metadataretriver.disable-8k": "true", "media.omx.LowLatency_mode": "1", "media.omx.display_mode": "3", "media.omx.multi_mode": "11", "media.player.cmcc_report.enable": "1", "media.player.errorcode": "3514", "media.player.read_report.timeout": "10", "media.player.rtsp_data_timeout.errorcode": "3515", "media.player.rtsp_recv_report.timeout": "10", "media.player.rtsp_report_error.enable": "1", "media.player.rtsp_teardown.enable": "1", "media.player.rtsperrorcode": "3515", "media.player.seekX": "0", "media.playstatus.enable": "1", "media.sf.omxvideo-optmize": "1", "media.support.dolbyvision": "true", "media.wfd.use-pcm-audio": "true", "media.wfd.videoframerate": "20", "media.wfd.videoreolutiongroup": "5", "media.wfd.videoreolutiontype": "0", "net.bt.name": "Android", "net.dhcp.check.option125.ok": "0", "net.dhcp.opt125": "CMCCJSHGU", "net.dhcp.send.release.pre.disc": "true", "net.dhcp6.ipoe.miss_opt17": "true", "net.dhcp6.send.release.pre.sol": "true", "net.dhcpc.ipver": "", "net.dhcpc.option": "", "net.dhcpc.pswd": "", "net.dhcpc.username": "", "net.dhcpv6.delay": "0", "net.dns1": "*************", "net.ethwifi.coexist": "true", "net.ethwifi.prior": "ethernet", "net.ethwifi.up": "1", "net.ppp.retrycount": "2", "net.pppoe.running": "1", "net.qtaguid_enabled": "1", "net.tcp.default_init_rwnd": "60", "net.tethering.noprovisioning": "true", "persist.audio.debug.read": "", "persist.audio.debug.search": "", "persist.audio.volume": "8", "persist.bluetooth.btsnoopenable": "false", "persist.bluetooth.btsnooppath": "/data/misc/bluedroid/btsnoop_hci.cfa", "persist.bluetooth.btsnoopsize": "0xffff", "persist.bluetooth.showdeviceswithoutnames": "false", "persist.blur.delayTime.enable": "false", "persist.buffer.delayTime.enable": "false", "persist.net.dhcp6.release.ip": "fd9e:4de:8271:0:4ae5:33ff:feb2:1c98/64", "persist.net.dhcp6.release.srvid": "AAMAATRH1Mvflw==", "persist.net.dhcpv4.dns1": "**********", "persist.net.dhcpv4.dns2": "**************", "persist.net.monitor": "true", "persist.net.pppoev4.dns1": "**********", "persist.net.pppoev4.dns2": "**************", "persist.netd.stable_secret": "1a26:f230:b1e9:6f93:2abd:f102:39:6467", "persist.network.coexist": "true", "persist.prop.optimalfmt.enable": "true", "persist.service.ki.builtin": "0", "persist.suspend.sleep.delay": "1000", "persist.sys.amlogic.ipv6.ipoe": "false", "persist.sys.amlogic.ipv6.option125": "", "persist.sys.amlogic.ipv6.pwd": "", "persist.sys.amlogic.ipv6.usr": "", "persist.sys.app.installerStart": "true", "persist.sys.app.rotation": "middle_port", "persist.sys.app.silentInstaller": "true", "persist.sys.autosuspend.hdmi": "true", "persist.sys.autosuspend.timeout": "300000", "persist.sys.boot.fail_count": "0", "persist.sys.boot.reason": "shutdown,userrequested", "persist.sys.boot.volume": "6", "persist.sys.bootanim.delconfig": "true", "persist.sys.bootvideo": "50", "persist.sys.cec.enable.key": "true", "persist.sys.cec.enable.volume.key": "false", "persist.sys.country": "CN,HK,US", "persist.sys.dalvik.vm.lib.2": "libart.so", "persist.sys.dhcp6c.release.ipaddress": "fd9e:4de:8271:0:4ae5:33ff:feb2:1c98/64", "persist.sys.dns.protect.enable": "false", "persist.sys.hdmi.addr.playback": "4", "persist.sys.hdmi.keep_awake": "false", "persist.sys.hls.burst": "-1", "persist.sys.isshowime": "1", "persist.sys.itms.epg.launcher": "tv.icntv.ott", "persist.sys.language": "ZH,EN", "persist.sys.locale": "zh-CN", "persist.sys.localevar": "", "persist.sys.mic_gain_value": "-14.3,-13,-11.9,-10.9,-9,-7.4,-6.1,-4.9,-3.9,-3,-2.1,-1.5,-0.7,-0.1,0", "persist.sys.mount.delay": "true", "persist.sys.mscanner": "false", "persist.sys.optimalfmt.perfer": "max_fmt", "persist.sys.reverb_mode": "0,4,5", "persist.sys.scan.optimize": "true", "persist.sys.stb.manu_info": "CM311-1e", "persist.sys.tcpdump.capture": "false", "persist.sys.timezone": "Asia/Shanghai", "persist.sys.tv.dpi": "3840*2160", "persist.sys.tv.size": "52", "persist.sys.tv.type": "UGREEN-UHD", "persist.sys.tv.week": "5", "persist.sys.tv.year": "2022", "persist.sys.udp.multimeout": "10", "persist.sys.usb.config": "none", "persist.sys.video.contentmode": "2", "persist.sys.webview.vmsize": "104857600", "persist.sys.wifi.dualstack": "10", "persist.unload.delayTime.enable": "false", "persist.vendor.bluetooth.prefferedrole": "master", "persist.vendor.bluetooth.rtkcoex": "true", "persist.vendor.bt_name": "rtl_bt", "persist.vendor.bt_vendor": "libbt-vendor_rtl.so", "persist.vendor.libbt_vendor": "libbt-vendor_rtlMulti.so", "persist.vendor.media.bootvideo": "0050", "persist.vendor.rtkbt.bdaddr_path": "none", "persist.vendor.rtkbtadvdisable": "false", "persist.vendor.standby.bootUp": "true", "persist.vendor.sys.cec.autowakeup": "true", "persist.vendor.sys.cec.controlenabled": "false", "persist.vendor.sys.cec.deviceautopoweroff": "true", "persist.vendor.wifi_remove": "true", "pm.dexopt.ab-ota": "speed-profile", "pm.dexopt.bg-dexopt": "speed-profile", "pm.dexopt.boot": "verify", "pm.dexopt.first-boot": "quicken", "pm.dexopt.inactive": "verify", "pm.dexopt.install": "speed-profile", "pm.dexopt.priv-apps-oob": "false", "pm.dexopt.priv-apps-oob-list": "ALL", "pm.dexopt.shared": "speed", "poweroff.doubleclick": "1", "qcom.bluetooth.soc": "rome_uart", "ro.actionable_compatible_property.enabled": "true", "ro.af.client_heap_size_kbyte": "1536", "ro.allow.mock.location": "0", "ro.andlink.OS": "android9.0", "ro.andlink.authId": "0041130010012580240948E533B21C98", "ro.andlink.authKey": "", "ro.andlink.authMode": "1", "ro.andlink.authkey": "00010001EE7717ADC41AFF48DE57EADE521FD7B93715A2474437E6C266330D1D1C768FD9", "ro.andlink.bluetoothMacAddress": "8cea12705947", "ro.andlink.chips.factory": "amlogic", "ro.andlink.chips.model": "armeabi-v7a", "ro.andlink.chips.type": "arm", "ro.andlink.cmei": "", "ro.andlink.cpuModel": "S905L3S", "ro.andlink.deviceBrand": "CM311-1e", "ro.andlink.deviceMac": "48E533B21C98", "ro.andlink.deviceModel": "CM311-1e", "ro.andlink.deviceVendor": "CMDC", "ro.andlink.firmwareVersion": "V.686.03", "ro.andlink.mac": "48E533B21C98", "ro.andlink.manuDate": "2024-06", "ro.andlink.manufacturer": "CMDC", "ro.andlink.powerSupplyMode": "220V", "ro.andlink.ramStorageSize": "16GB", "ro.andlink.romStorageSize": "2GB", "ro.andlink.sn": "0041130010012580240948E533B21C98", "ro.andlink.stbId": "0041130010012580240948E533B21C98", "ro.andlink.stbMode": "OTT", "ro.andlink.wifiMode": "1", "ro.art.hiddenapi.warning": "1", "ro.audio.mapvalue": "0,0,0,0", "ro.audio.usb.period_us": "10000", "ro.autocreate.download": "true", "ro.baseband": "unknown", "ro.bionic.ld.warning": "1", "ro.bluename.model.first": "1", "ro.board.platform": "CM311-1e", "ro.boot.boardid": "", "ro.boot.bootreason": "shutdown,userrequested", "ro.boot.deviceid": "111001390404704", "ro.boot.dtbo_idx": "0", "ro.boot.fake_battery": "42", "ro.boot.firstboot": "1", "ro.boot.hardware": "amlogic", "ro.boot.mac": "48:E5:33:B2:1C:98", "ro.boot.selinux": "permissive", "ro.boot.serialno": "0041130010012580240948E533B21C98", "ro.boot.stbid": "0041130010012580240948E533B21C98", "ro.bootimage.build.date": "Fri Jun 7 08:34:45 CST 2024", "ro.bootimage.build.date.utc": "1717720485", "ro.bootimage.build.fingerprint": "p291_iptv/p291_iptv/p291_iptv:9/PPR1.180610.011/20240607:userdebug/test-keys", "ro.bootloader": "unknown", "ro.bootmac": "48:E5:33:B2:1C:98", "ro.bootmode": "unknown", "ro.boottime.adbd": "44794115561", "ro.boottime.audioserver": "**********", "ro.boottime.bluetooth_init": "**********", "ro.boottime.bootanim": "**********", "ro.boottime.cameraserver": "**********", "ro.boottime.chservice": "**********", "ro.boottime.console": "**********", "ro.boottime.drm": "**********", "ro.boottime.dumpstate-1-0": "**********", "ro.boottime.ext_bootfail": "**********", "ro.boottime.flash_recovery": "**********", "ro.boottime.gatekeeperd": "**********", "ro.boottime.hdmicecd": "**********", "ro.boottime.health-hal-2-0": "**********", "ro.boottime.hidl_memory": "**********", "ro.boottime.hwservicemanager": "**********", "ro.boottime.imageserver": "**********", "ro.boottime.incidentd": "**********", "ro.boottime.init": "715", "ro.boottime.init.cold_boot_wait": "161", "ro.boottime.init.mount_all.default": "388", "ro.boottime.init.selinux": "62", "ro.boottime.installd": "**********", "ro.boottime.iptvd": "**********", "ro.boottime.keystore": "**********", "ro.boottime.lmkd": "**********", "ro.boottime.load_remote": "10366616878", "ro.boottime.logd": "**********", "ro.boottime.logd-reinit": "**********", "ro.boottime.mdnsd": "44826579061", "ro.boottime.media": "**********", "ro.boottime.mediadrm": "**********", "ro.boottime.mediaextractor": "**********", "ro.boottime.mediametrics": "**********", "ro.boottime.miracast_hdcp2": "**********", "ro.boottime.netd": "**********", "ro.boottime.perfprofd": "**********", "ro.boottime.remotecfg": "**********", "ro.boottime.screen_control": "**********5", "ro.boottime.servicemanager": "3407600167", "ro.boottime.softprobe": "10372818087", "ro.boottime.statsd": "4817435417", "ro.boottime.storaged": "4823192209", "ro.boottime.subtitleserver": "4065349625", "ro.boottime.sunniwell_setup": "4066493917", "ro.boottime.surfaceflinger": "**********", "ro.boottime.swRootService": "**********", "ro.boottime.swiperf_setup": "**********", "ro.boottime.system_control": "**********", "ro.boottime.thermalcfg": "10382810795", "ro.boottime.thermalservice": "**********", "ro.boottime.tombstoned": "**********", "ro.boottime.ueventd": "**********", "ro.boottime.uninstall": "10343649212", "ro.boottime.usbd": "**********", "ro.boottime.vendor.audio-hal-2-0": "**********", "ro.boottime.vendor.bluetooth-1-0": "**********", "ro.boottime.vendor.camera-provider-2-4": "**********", "ro.boottime.vendor.cec-hal-1-0": "**********", "ro.boottime.vendor.configstore-hal": "**********", "ro.boottime.vendor.gatekeeper-1-0": "**********", "ro.boottime.vendor.gralloc-2-0": "**********", "ro.boottime.vendor.hwcomposer-2-2": "**********", "ro.boottime.vendor.ir-hal-1-0": "**********", "ro.boottime.vendor.keymaster-3-0": "**********", "ro.boottime.vendor.light-hal-2-0": "**********", "ro.boottime.vendor.media.omx": "**********", "ro.boottime.vendor.memtrack-hal-1-0": "**********", "ro.boottime.vendor.power-hal-1-0": "**********", "ro.boottime.vendor.thermal-hal-1-0": "**********", "ro.boottime.vendor.usb-hal-1-0": "**********", "ro.boottime.vendor.wifi_hal_legacy": "**********", "ro.boottime.vndservicemanager": "**********", "ro.boottime.vold": "**********", "ro.boottime.wifi_preload": "**********", "ro.boottime.wificond": "**********", "ro.boottime.xiriservice": "10341814462", "ro.boottime.zygote": "**********", "ro.build.aml.baseline": "\"AmlogicIPTVAndroidP_R210531_T20220709\"", "ro.build.characteristics": "device,mbx,nosdcard", "ro.build.date": "Thu Jun 13 19:56:28 CST 2024", "ro.build.date.utc": "1718279788", "ro.build.description": "Amlogic905L3-eng 9.0.0 CM311-1e V.686.03 test-keys", "ro.build.devicemodel": "CM311-1e", "ro.build.display.id": "CM311-1e test-keys", "ro.build.equipment": "CM311-1e", "ro.build.expect.bootloader": "01.01.180822.145544", "ro.build.fingerprint": "AndroidP/CMDC/S905L3:9.0.0/CM311-1e/V.686.03:eng/test-keys", "ro.build.flavor": "p291_iptv-userdebug", "ro.build.host": "cmcc", "ro.build.id": "CM311-1e", "ro.build.product": "CM311-1e", "ro.build.system_root_image": "false", "ro.build.tags": "test-keys", "ro.build.type": "userdebug", "ro.build.user": "jenkins", "ro.build.version.all_codenames": "REL", "ro.build.version.base_os": "", "ro.build.version.codename": "REL", "ro.build.version.incremental": "V.686.03", "ro.build.version.min_supported_target_sdk": "17", "ro.build.version.preview_sdk": "0", "ro.build.version.release": "9", "ro.build.version.sdk": "28", "ro.build.version.security_patch": "2018-08-05", "ro.carrier": "unknown", "ro.chip.type": "AmlogicS905L3S", "ro.chservice.version": "cmdc.***********", "ro.config.alarm_alert": "Alarm_Classic.ogg", "ro.config.low_ram": "true", "ro.config.media_vol_default": "8", "ro.config.notification_sound": "pixiedust.ogg", "ro.config.ringtone": "Ring_Synth_04.ogg", "ro.crypto.fuse_sdcard": "true", "ro.crypto.state": "unencrypted", "ro.crypto.volume.filenames_mode": "aes-256-cts", "ro.custom.settings.packagename": "net.sunniwell.app.swsettings", "ro.dalvik.vm.native.bridge": "0", "ro.data.free.size": "500", "ro.debuggable": "1", "ro.deviceid": "111001390404704", "ro.devicetype": "stb", "ro.ethernet.def_ipv4_mode": "ipoe", "ro.ethernet.def_ipv4_on": "true", "ro.ethernet.def_ipv4_pwd": "JS@HGU", "ro.ethernet.def_ipv6_on": "true", "ro.expect.recovery_id": "0xaec1b1337781fdfd854032e494c220f7ae718f1f000000000000000000000000", "ro.factory.devicemodel": "CM311-1e", "ro.factory.name": "CMDC", "ro.factory.oui": "FF0001", "ro.firstboot": "1", "ro.flash.size": "16GB", "ro.hardware": "amlogic", "ro.hardwareno": "CM311-1e", "ro.hdmi.device_type": "4", "ro.hdmi.set_menu_language": "false", "ro.hls.default": "true", "ro.iptv.mbox": "true", "ro.logd.size.stats": "64K", "ro.mac": "48:E5:33:B2:1C:98", "ro.media.camera_preview.limitedrate": "1920x1080x30,1280x720x30,640x480x30,320x240x28", "ro.media.camera_preview.maxsize": "1920x1080", "ro.media.camera_preview.usemjpeg": "1", "ro.media.camera_usb.faceback": "false", "ro.media.dolby": "0", "ro.media.maxmem": "629145600", "ro.media.maxresolution": "0", "ro.media.softprobe.enable": "true", "ro.media.timeshift": "0", "ro.memory.size": "2GB", "ro.migukaraok": "true", "ro.mountStorage.autopop": "true", "ro.mountStorage.showFileManager": "true", "ro.net.pppoe": "true", "ro.no_wifi": "1", "ro.opengles.version": "131072", "ro.persistent_properties.ready": "true", "ro.product.board": "AmlogicS905L3S", "ro.product.boardmanufacturer": "amlogic", "ro.product.brand": "CM311-1e", "ro.product.build.date": "Fri Jun 7 08:34:45 CST 2024", "ro.product.build.date.utc": "1717720485", "ro.product.build.fingerprint": "p291_iptv/p291_iptv/p291_iptv:9/PPR1.180610.011/20240607:userdebug/test-keys", "ro.product.ch.hw.ver": "CM311-1e", "ro.product.cpu.abi": "armeabi-v7a", "ro.product.cpu.abi2": "<PERSON><PERSON><PERSON>", "ro.product.cpu.abilist": "armeabi-v7a,armeabi", "ro.product.cpu.abilist32": "armeabi-v7a,armeabi", "ro.product.cpu.abilist64": "", "ro.product.cpu.platform": "amlogic", "ro.product.device": "p291_iptv", "ro.product.first_api_level": "28", "ro.product.flash.info": "16G", "ro.product.locale": "zh-CN", "ro.product.locale.language": "zh", "ro.product.locale.region": "ZH,EN", "ro.product.manufacturer": "CMDC", "ro.product.manufactureroui": "FF0001", "ro.product.model": "CM311-1e", "ro.product.name": "CM311-1e", "ro.product.stb.serialnum": "0041130010012580240948E533B21C98", "ro.product.stb.stbid": "0041130010012580240948E533B21C98", "ro.product.stb.tvid": "0041130010012580240948E533B21C98", "ro.product.type": "0", "ro.product.vendor.brand": "CM311-1e", "ro.product.vendor.device": "CM311-1e", "ro.product.vendor.manufacturer": "CMDC", "ro.product.vendor.model": "CM311-1e", "ro.product.vendor.name": "CM311-1e", "ro.prop.heaac.filter": "false", "ro.property_service.version": "2", "ro.radio.noril": "false", "ro.revision": "0", "ro.runtime.firstboot": "1753774889720", "ro.sdcardfs.support": "true", "ro.secure": "1", "ro.serialno": "0041130010012580240948E533B21C98", "ro.sf.disable_triple_buffer": "1", "ro.sf.lcd_density": "240", "ro.standby.action": "net.sunniwell.action.POWER_WINDOW_SERVICE", "ro.stbid": "0041130010012580240948E533B21C98", "ro.sw.checkInfo": "26", "ro.sw.direct.pkgName": "com.aspirecn.activate", "ro.sw.logo.recovery": "true", "ro.sw.packagename.usbupgrade": "net.sunniwell.app.upgrade.usb", "ro.sw.product.province": "JS", "ro.sw.publish_region": "cmcc_js", "ro.sw.stb_manu": "ZG", "ro.sw.stb_model": "CM311_1e_2_16_B", "ro.sw.toast_low_version": "false", "ro.treble.enabled": "true", "ro.vendor.app.optimization": "true", "ro.vendor.autoconnectbt.btclass": "50c", "ro.vendor.autoconnectbt.isneed": "false", "ro.vendor.autoconnectbt.macprefix": "00:CD:FF", "ro.vendor.autoconnectbt.nameprefix": "Amlogic_RC", "ro.vendor.autoconnectbt.rssilimit": "70", "ro.vendor.btmodule": "multibt", "ro.vendor.build.date": "Fri Jun 7 08:34:45 CST 2024", "ro.vendor.build.date.utc": "1717720485", "ro.vendor.build.fingerprint": "AndroidP/AmlogicS905L3/Amlogic905L3:9.0.0/CM311-1e/V.686.03:eng/test-keys", "ro.vendor.build.security_patch": "", "ro.vendor.low_power_default_color": "true", "ro.vendor.platform.disable.audiorawout": "false", "ro.vendor.platform.has.mbxuimode": "true", "ro.vendor.platform.has.realoutputmode": "true", "ro.vendor.platform.hdmi.device_type": "4", "ro.vendor.platform.is.tv": "0", "ro.vendor.platform.need.display.hdmicec": "true", "ro.vendor.platform.omx": "true", "ro.vendor.platform.support.dolby": "true", "ro.vendor.platform.support.dolbyvision": "true", "ro.vendor.platform.support.dts": "true", "ro.vendor.platform.support.network_led": "true", "ro.vendor.platform.usehwmjpeg": "true", "ro.vendor.product.cpu.abilist": "armeabi-v7a,armeabi", "ro.vendor.product.cpu.abilist32": "armeabi-v7a,armeabi", "ro.vendor.product.cpu.abilist64": "", "ro.vendor.sdr2hdr.enable": "true", "ro.vendor.tcpdump.enabled": "false", "ro.vendor.vndk.version": "26.1.0", "ro.vndk.version": "28", "ro.wifi.channels": "", "ro.zygote": "zygote32", "security.perf_harden": "1", "selinux.restorecon_recursive": "/data/misc_ce/0", "service.adb.root": "1", "service.adb.tcp.port": "5555", "service.bootanim.exit": "1", "service.bootvideo": "0", "service.bootvideo.exit": "0", "service.media.playstatus": "stopped", "service.migu_bootanim.exit": "0", "service.sf.present_timestamp": "1", "sys.amplayer.drop_pcm": "1", "sys.app.oom_adj": "1", "sys.boot.kpi": "false", "sys.boot.reason": "shutdown,userrequested", "sys.boot_completed": "1", "sys.broadcast.permit": "true", "sys.cmcc.hls.adaptrate": "", "sys.cmcc.hls.firstrate": "", "sys.cmcc.video.contentmode": "", "sys.debug.system.prop": "/system/build_CM311-1e-ZG.prop", "sys.debug.vendor.prop": "/vendor/build_CM311-1e-ZG.prop", "sys.deepdiagnose.support": "1", "sys.deflaunchersetting.pkg": "com.shcmcc.setting", "sys.imgplayer.freq_down": "1", "sys.install.mode": "chinamobile", "sys.iptv.rtsp.transport": "0", "sys.key.direcet.report": "true", "sys.logbootcomplete": "1", "sys.network.currentgateway.v4": "eth", "sys.network.currentgateway.v6": "", "sys.network.priority": "6", "sys.platform.media": "mlogic", "sys.pop.ups": "true", "sys.proj.tender.type": "<PERSON><PERSON><PERSON>", "sys.proj.type": "mobile", "sys.rescue_boot_count": "1", "sys.retaildemo.enabled": "0", "sys.settings.support": "1", "sys.settings.support.ap.flags": "0", "sys.settings.support.bluetooth": "1", "sys.settings.support.languages": "zh-CN", "sys.settings.support.net.flags": "7", "sys.settings.support.spdif": "0", "sys.shcmcc.test.hideview": "true", "sys.standby.mode": "1", "sys.start.launcher": "1", "sys.sunniwell.v_unsupport": "false", "sys.sw.adb.enabled": "1", "sys.sw.audio.hdmi": "0", "sys.sw.audio.spdif": "0", "sys.sw.ctrl.is_kill_unneed": "true", "sys.sw.hls.adaptrate": "0", "sys.sw.hls.firstrate": "3", "sys.sw.local.packagename": "net.sunniwell.app.localplayer.sh", "sys.sw.param.backup.enable": "enable", "sys.sw.remote.deviceId": "3", "sys.sw.video.contentmode1": "2", "sys.switch.dns.enable": "true", "sys.sysctl.extra_free_kbytes": "24300", "sys.sysctl.tcp_def_init_rwnd": "60", "sys.usb.config": "mtp", "sys.usb.configfs": "1", "sys.usb.controller": "c9100000.dwc2_a", "sys.usb.ffs.ready": "1", "sys.usb.state": "mtp", "sys.user.0.ce_available": "true", "sys.wifi.ipv6.enable": "true", "sys.wifitracing.started": "1", "sys.wipedata": "63", "tombstoned.max_tombstone_count": "50", "vendor.afbcd.enable": "1", "vendor.bluetooth.enable_timeout_ms": "11000", "vendor.display-size": "3840x2160", "vendor.hwc.forceOneLayer": "1", "vendor.ledlightred": "off", "vendor.media.liveplayer.enable-mediahal-videodec": "1", "vendor.media.liveplayer.enable-video-tunnel": "1", "vendor.media.liveplayer.mpeg2-use-ffmpeg-audio-decoder": "1", "vendor.sys.ch.model": "CM311-1e", "vendor.sys.ch.sw.ver": "V.686.03", "vendor.sys.hwc.booted": "true", "vendor.sys.videoratio.enable": "true", "vendor.system.support.dolbyvision": "false", "vold.has_adoptable": "1", "vold.has_quota": "1", "vold.has_reserved": "1", "vold.nick.enable": "true", "vold.post_fs_data_done": "1", "wc_transport.soc_initialized": "0", "webkit.loadurl.retry_cnt": "3", "webkit.loadurl.timeout": "10", "wifi.direct.interface": "p2p0", "wifi.interface": "wlan0"}, "security_properties": {"dalvik.vm.dex2oat-minidebuginfo": "true", "debug.atrace.tags.enableflags": "0", "debug.force_rtl": "0", "debug.hwui.use_buffer_age": "false", "debug.sf.disable_backpressure": "1", "debug.sf.latch_unsignaled": "1", "init.svc.adbd": "running", "init.svc.keystore": "running", "init.svc.swRootService": "running", "init.svc.vendor.keymaster-3-0": "running", "libplayer.cache.debug": "1", "media.amplayer.seekkeyframe": "1", "persist.audio.debug.read": "", "persist.audio.debug.search": "", "persist.sys.cec.enable.key": "true", "persist.sys.cec.enable.volume.key": "false", "ro.andlink.authKey": "", "ro.andlink.authkey": "00010001EE7717ADC41AFF48DE57EADE521FD7B93715A2474437E6C266330D1D1C768FD9", "ro.boottime.adbd": "44794115561", "ro.boottime.keystore": "**********", "ro.boottime.swRootService": "**********", "ro.boottime.vendor.keymaster-3-0": "**********", "ro.build.system_root_image": "false", "ro.debuggable": "1", "ro.secure": "1", "service.adb.root": "1", "service.adb.tcp.port": "5555", "sys.debug.system.prop": "/system/build_CM311-1e-ZG.prop", "sys.debug.vendor.prop": "/vendor/build_CM311-1e-ZG.prop", "sys.key.direcet.report": "true", "sys.sw.adb.enabled": "1"}, "signature_verification": {"dm_verity_status": null, "selinux_status": "Permissive", "verified_boot": null, "security_patch": "2018-08-05"}, "processes": [], "packages": ["com.iflytek.xiri", "net.sunniwell.service.platform", "tv.icntv.ott", "net.sunniwell.app.api", "net.sunniwell.app.swsettings", "com.android.providers.media", "com.amlogic.multimediaplayer", "net.sunniwell.app.download", "com.android.companiondevicemanager", "net.sunniwell.service.mediamonitor", "com.droidlogic.mediacenter", "com.droidlogic", "net.sunniwell.swinformation", "net.sunniwell.app.upgrade.online", "net.sunniwell.app.localplayer.sh", "net.sunniwell.app.init", "net.sunniwell.app.itms", "com.aspirecn.jshdc.personalcenter", "com.aspirecn.middleware.authjs", "com.android.defcontainer", "com.cmdc.cmdcstbservice", "com.android.certinstaller", "android", "com.ysten.xmppapp", "net.sunniwell.service.stb.producttest", "net.sunniwell.capture", "com.droidlogic.FileBrower", "com.android.statementservice", "net.sunniwell.swswitchdns", "com.android.providers.settings", "net.sunniwell.app.upgrade.usb", "net.sunniwell.service.status", "com.aspirecn.jshdc.statistics", "com.iflytek.xiri2.system", "com.android.silenceinstaller", "com.android.webview", "com.aspirecn.jshdc.ad", "net.sunniwell.inputmethod.swpinyin2", "com.droidlogic.BluetoothRemote", "android.ext.shared", "com.chinamobile.sdk.upgrade", "com.android.gallery3d", "android.ext.services", "com.droidlogic.videoplayer", "tv.cmcc.vendor", "com.android.packageinstaller", "com.aspirecn.activate", "net.sunniwell.app.power", "com.android.managedprovisioning", "net.sunniwell.app.swemergency", "com.iflytek.bt.auto", "com.cmdc.service.burntest", "com.jscmcc.ottlogin", "com.aspirecn.jshdc.appstore", "com.aspirecn.jshdc.appusage", "com.gitv.launcher", "com.zte.authority_jscm", "com.android.shell", "com.android.providers.userdictionary", "net.sunniwell.service.standard.monitor", "com.android.systemui", "com.android.bluetooth", "com.cmcc.mid.softdetector", "com.sunniwell.swadvertise"], "partitions": {"mount_points": ["rootfs on / type rootfs (ro,seclabel)", "tmpfs on /dev type tmpfs (rw,seclabel,nosuid,relatime,mode=755)", "devpts on /dev/pts type devpts (rw,seclabel,relatime,mode=600,ptmxmode=000)", "proc on /proc type proc (rw,relatime)", "sysfs on /sys type sysfs (rw,seclabel,relatime)", "selinuxfs on /sys/fs/selinux type selinuxfs (rw,relatime)", "tmpfs on /mnt type tmpfs (rw,seclabel,nosuid,nodev,noexec,relatime,mode=755,gid=1000)", "/dev/block/odm on /odm type ext4 (ro,seclabel,relatime,block_validity,delalloc,nojournal_checksum,barrier,user_xattr,acl,inode_readahead_blks=8)", "/dev/block/product on /product type ext4 (ro,seclabel,relatime,block_validity,delalloc,nojournal_checksum,barrier,user_xattr,acl,inode_readahead_blks=8)", "/dev/block/system on /system type ext4 (ro,seclabel,relatime,block_validity,delalloc,barrier,user_xattr,acl,inode_readahead_blks=8)", "/dev/block/vendor on /vendor type ext4 (ro,seclabel,relatime,block_validity,delalloc,barrier,user_xattr,acl,inode_readahead_blks=8)", "none on /acct type cgroup (rw,nosuid,nodev,noexec,relatime,cpuacct)", "/sys/kernel/debug on /sys/kernel/debug type debugfs (rw,seclabel,relatime,mode=755)", "configfs on /sys/kernel/config type configfs (rw,relatime)", "none on /dev/stune type cgroup (rw,nosuid,nodev,noexec,relatime,schedtune)", "none on /config type configfs (rw,nosuid,nodev,noexec,relatime)", "none on /dev/cpuctl type cgroup (rw,nosuid,nodev,noexec,relatime,cpu)", "none on /dev/cpuset type cgroup (rw,nosuid,nodev,noexec,relatime,cpuset,noprefix,release_agent=/sbin/cpuset_release_agent)", "cg2_bpf on /dev/cg2_bpf type cgroup2 (rw,nosuid,nodev,noexec,relatime)", "bpf on /sys/fs/bpf type bpf (rw,nosuid,nodev,noexec,relatime)", "tmpfs on /tmp type tmpfs (rw,seclabel,relatime)", "tmpfs on /tmp/playInfoLog type tmpfs (rw,seclabel,relatime)", "tmpfs on /tmp/capture type tmpfs (rw,seclabel,relatime)", "/dev/block/data on /data type ext4 (rw,seclabel,nosuid,nodev,noatime,nodelalloc,resgid=1065,errors=panic,data=ordered)", "/dev/block/cache on /cache type ext4 (rw,seclabel,nosuid,nodev,noatime,nodelalloc,errors=panic,data=ordered)", "/dev/block/metadata on /metadata type ext4 (rw,seclabel,nosuid,nodev,noatime,nodelalloc,errors=panic,data=ordered)", "/dev/block/param on /mnt/vendor/param type ext4 (rw,seclabel,nosuid,nodev,noatime,nodelalloc,errors=panic,data=ordered)", "/dev/block/tee on /mnt/vendor/tee type ext4 (rw,seclabel,nosuid,nodev,noatime,nodelalloc,errors=panic,data=ordered)", "/dev/block/backup on /backup type ext4 (rw,seclabel,nosuid,nodev,noatime,block_validity,nojournal_checksum,barrier,user_xattr,acl,errors=panic)", "tmpfs on /storage type tmpfs (rw,seclabel,nosuid,nodev,noexec,relatime,mode=755,gid=1000)", "adb on /dev/usb-ffs/adb type functionfs (rw,relatime)", "/data/media on /mnt/runtime/default/emulated type sdcardfs (rw,nosuid,nodev,noexec,noatime,fsuid=1023,fsgid=1023,gid=1015,multiuser,mask=6,derive_gid,default_normal)", "/data/media on /storage/emulated type sdcardfs (rw,nosuid,nodev,noexec,noatime,fsuid=1023,fsgid=1023,gid=1015,multiuser,mask=6,derive_gid,default_normal)", "/data/media on /mnt/runtime/read/emulated type sdcardfs (rw,nosuid,nodev,noexec,noatime,fsuid=1023,fsgid=1023,gid=9997,multiuser,mask=23,derive_gid,default_normal)", "/data/media on /mnt/runtime/write/emulated type sdcardfs (rw,nosuid,nodev,noexec,noatime,fsuid=1023,fsgid=1023,gid=9997,multiuser,mask=7,derive_gid,default_normal)"], "disk_usage": ["Filesystem          1K-blocks   Used Available Use% Mounted on", "tmpfs                 1012828    536   1012292   1% /dev", "tmpfs                 1012828      0   1012828   0% /mnt", "/dev/block/odm         126960    556    126404   1% /odm", "/dev/block/product     126960  53972     72988  43% /product", "/dev/block/system     1290112 806456    483656  63% /system", "/dev/block/vendor      317392 238088     79304  76% /vendor", "tmpfs                 1012828      0   1012828   0% /tmp", "tmpfs                 1012828      0   1012828   0% /tmp/playInfoLog", "tmpfs                 1012828      0   1012828   0% /tmp/capture", "/dev/block/data      10612308 916188   9696120   9% /data", "/dev/block/cache       999320   2172    997148   1% /cache", "/dev/block/metadata     11760     40     11720   1% /metadata", "/dev/block/param        11760    876     10884   8% /mnt/vendor/param", "/dev/block/tee          27632     28     27604   1% /mnt/vendor/tee", "/dev/block/backup     1032088 566680    465408  55% /backup", "/data/media          10612308 916188   9696120   9% /mnt/runtime/default/emulated"]}}