#!/system/bin/sh

files=/mnt/vendor/param/backup/swbackparam.txt

if [ ! -f $files ]
then
  exit  
fi 

while read line;
do
	if [[ ${line:0:5} = "file=" ]]
	then 
		filepath=`echo $line | busybox awk -F "=" '{print $2}' | busybox awk -F " " '{print $1}' | busybox tr -d " "`	
		uid=`echo $line | busybox awk -F "=" '{print $2}' | busybox awk -F " " '{print $2}' | busybox tr -d " "`	
		gid=`echo $line | busybox awk -F "=" '{print $2}' | busybox awk -F " " '{print $3}' | busybox tr -d " "`	
		mod=`echo $line | busybox awk -F "=" '{print $2}' | busybox awk -F " " '{print $4}' | busybox tr -d " "`	
		#read filePath uid gid mod <<< $(echo `echo $line |busybox awk -F 'file=' '{print $2}' | busybox tr -d " "`)
		echo filepath=$filepath,uid=$uid,gid=$gid,mod=$mod 
		if [ -f /mnt/vendor/param/backup/$filepath ]
		then 
			file=`echo $filepath | busybox awk -F  "/" '{print $NF}'`
			path=`echo ${filepath%$file*}`
			if [ ! -d "$path" ];then
				mkdir -p $path
			fi 

			if [ ! -f $filepath ];then
				cp /mnt/vendor/param/backup$filepath $filepath 
				chmod $mod $filepath
				chown $uid:$gid $filepath 
			fi 
		fi 
  	fi 
done < $files

rm -rf /mnt/vendor/param/backup/data
