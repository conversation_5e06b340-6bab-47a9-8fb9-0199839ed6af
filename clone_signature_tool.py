#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
签名克隆工具
从原始update.zip提取签名，应用到update_diy.zip，并进行签名比对

Author: By.举个🌰
Date: 2025
Copyright: By.举个🌰
"""

import os
import sys
import hashlib
import binascii
import zipfile
import shutil
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import tempfile
import datetime

class SignatureCloneTool:
    """签名克隆工具"""
    
    def __init__(self):
        self.work_dir = Path(".")
        self.original_zip = "update.zip"
        self.target_zip = "update_diy.zip"
        
    def log(self, message: str):
        """输出日志"""
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        print(f"[{timestamp}] {message}")
    
    def extract_original_signature(self) -> Optional[Dict]:
        """从原始update.zip提取签名信息"""
        self.log("🔍 从原始update.zip提取签名...")
        
        original_path = Path(self.original_zip)
        if not original_path.exists():
            self.log(f"❌ 找不到原始文件: {self.original_zip}")
            return None
        
        signature_info = {
            'manifest': None,
            'cert_sf': None,
            'cert_rsa': None,
            'cert_files': {}
        }
        
        try:
            with zipfile.ZipFile(original_path, 'r') as zip_ref:
                file_list = zip_ref.namelist()
                
                # 查找签名文件
                for file_name in file_list:
                    if file_name == "META-INF/MANIFEST.MF":
                        signature_info['manifest'] = zip_ref.read(file_name)
                        self.log("✅ 找到 MANIFEST.MF")
                    elif file_name == "META-INF/CERT.SF":
                        signature_info['cert_sf'] = zip_ref.read(file_name)
                        self.log("✅ 找到 CERT.SF")
                    elif file_name.startswith("META-INF/") and (file_name.endswith(".RSA") or file_name.endswith(".DSA")):
                        signature_info['cert_rsa'] = zip_ref.read(file_name)
                        signature_info['cert_files'][file_name] = zip_ref.read(file_name)
                        self.log(f"✅ 找到证书文件: {file_name}")
                
                if not signature_info['manifest']:
                    self.log("❌ 原始文件没有签名信息")
                    return None
                
                self.log("✅ 原始签名信息提取完成")
                return signature_info
                
        except Exception as e:
            self.log(f"❌ 提取签名失败: {str(e)}")
            return None
    
    def create_new_manifest(self, target_zip_path: Path) -> str:
        """为目标ZIP创建新的MANIFEST.MF"""
        self.log("📋 为目标文件创建新的MANIFEST.MF...")
        
        manifest_lines = [
            "Manifest-Version: 1.0",
            "Created-By: Signature Clone Tool - By.举个🌰",
            ""
        ]
        
        try:
            with zipfile.ZipFile(target_zip_path, 'r') as zip_ref:
                for file_info in zip_ref.filelist:
                    # 跳过META-INF目录和目录条目
                    if file_info.filename.startswith("META-INF/") or file_info.filename.endswith("/"):
                        continue
                    
                    # 读取文件内容并计算哈希
                    file_data = zip_ref.read(file_info.filename)
                    
                    # 计算SHA-1和SHA-256哈希
                    sha1_hash = hashlib.sha1(file_data).digest()
                    sha256_hash = hashlib.sha256(file_data).digest()
                    
                    # Base64编码
                    sha1_b64 = binascii.b2a_base64(sha1_hash).decode().strip()
                    sha256_b64 = binascii.b2a_base64(sha256_hash).decode().strip()
                    
                    # 添加到清单
                    manifest_lines.extend([
                        f"Name: {file_info.filename}",
                        f"SHA1-Digest: {sha1_b64}",
                        f"SHA-256-Digest: {sha256_b64}",
                        ""
                    ])
            
            manifest_content = "\n".join(manifest_lines)
            self.log(f"✅ 新MANIFEST.MF创建完成，包含 {len([l for l in manifest_lines if l.startswith('Name:')])} 个文件")
            return manifest_content
            
        except Exception as e:
            self.log(f"❌ 创建MANIFEST.MF失败: {str(e)}")
            return ""
    
    def create_new_cert_sf(self, manifest_content: str) -> str:
        """创建新的CERT.SF文件"""
        self.log("🔐 创建新的CERT.SF...")
        
        # 计算整个清单的哈希
        manifest_bytes = manifest_content.encode('utf-8')
        manifest_sha1 = hashlib.sha1(manifest_bytes).digest()
        manifest_sha256 = hashlib.sha256(manifest_bytes).digest()
        
        manifest_sha1_b64 = binascii.b2a_base64(manifest_sha1).decode().strip()
        manifest_sha256_b64 = binascii.b2a_base64(manifest_sha256).decode().strip()
        
        cert_sf_lines = [
            "Signature-Version: 1.0",
            "Created-By: Signature Clone Tool - By.举个🌰",
            f"SHA1-Digest-Manifest: {manifest_sha1_b64}",
            f"SHA-256-Digest-Manifest: {manifest_sha256_b64}",
            ""
        ]
        
        # 为每个文件条目创建签名条目
        manifest_lines = manifest_content.split('\n')
        current_entry = []
        
        for line in manifest_lines:
            if line.startswith("Name: "):
                if current_entry:
                    # 处理前一个条目
                    entry_content = '\n'.join(current_entry) + '\n'
                    entry_sha1 = hashlib.sha1(entry_content.encode()).digest()
                    entry_sha256 = hashlib.sha256(entry_content.encode()).digest()
                    
                    entry_sha1_b64 = binascii.b2a_base64(entry_sha1).decode().strip()
                    entry_sha256_b64 = binascii.b2a_base64(entry_sha256).decode().strip()
                    
                    cert_sf_lines.extend([
                        current_entry[0],  # Name: 行
                        f"SHA1-Digest: {entry_sha1_b64}",
                        f"SHA-256-Digest: {entry_sha256_b64}",
                        ""
                    ])
                
                current_entry = [line]
            elif line.strip() and current_entry:
                current_entry.append(line)
            elif not line.strip() and current_entry:
                # 处理最后一个条目
                entry_content = '\n'.join(current_entry) + '\n'
                entry_sha1 = hashlib.sha1(entry_content.encode()).digest()
                entry_sha256 = hashlib.sha256(entry_content.encode()).digest()
                
                entry_sha1_b64 = binascii.b2a_base64(entry_sha1).decode().strip()
                entry_sha256_b64 = binascii.b2a_base64(entry_sha256).decode().strip()
                
                cert_sf_lines.extend([
                    current_entry[0],  # Name: 行
                    f"SHA1-Digest: {entry_sha1_b64}",
                    f"SHA-256-Digest: {entry_sha256_b64}",
                    ""
                ])
                current_entry = []
        
        cert_sf_content = "\n".join(cert_sf_lines)
        self.log("✅ 新CERT.SF创建完成")
        return cert_sf_content
    
    def apply_cloned_signature(self, target_zip_path: Path, signature_info: Dict) -> bool:
        """将克隆的签名应用到目标ZIP"""
        self.log(f"✍️ 应用克隆签名到 {target_zip_path.name}...")
        
        try:
            with tempfile.TemporaryDirectory() as temp_dir:
                temp_path = Path(temp_dir)
                temp_zip = temp_path / "temp.zip"
                
                # 复制目标文件
                shutil.copy2(target_zip_path, temp_zip)
                
                # 移除现有签名
                self.remove_existing_signature(temp_zip)
                
                # 创建新的签名文件
                manifest_content = self.create_new_manifest(temp_zip)
                cert_sf_content = self.create_new_cert_sf(manifest_content)
                
                # 创建META-INF目录
                meta_inf_dir = temp_path / "META-INF"
                meta_inf_dir.mkdir(exist_ok=True)
                
                # 写入签名文件
                manifest_path = meta_inf_dir / "MANIFEST.MF"
                with open(manifest_path, 'w', encoding='utf-8') as f:
                    f.write(manifest_content)
                
                cert_sf_path = meta_inf_dir / "CERT.SF"
                with open(cert_sf_path, 'w', encoding='utf-8') as f:
                    f.write(cert_sf_content)
                
                # 复制原始证书文件
                for cert_filename, cert_data in signature_info['cert_files'].items():
                    cert_path = meta_inf_dir / Path(cert_filename).name
                    with open(cert_path, 'wb') as f:
                        f.write(cert_data)
                
                # 将签名文件添加到ZIP
                with zipfile.ZipFile(temp_zip, 'a') as zip_ref:
                    zip_ref.write(manifest_path, "META-INF/MANIFEST.MF")
                    zip_ref.write(cert_sf_path, "META-INF/CERT.SF")
                    
                    for cert_filename in signature_info['cert_files'].keys():
                        cert_name = Path(cert_filename).name
                        cert_path = meta_inf_dir / cert_name
                        zip_ref.write(cert_path, cert_filename)
                
                # 替换原文件
                shutil.copy2(temp_zip, target_zip_path)
                
            self.log("✅ 克隆签名应用完成")
            return True
            
        except Exception as e:
            self.log(f"❌ 应用签名失败: {str(e)}")
            return False
    
    def remove_existing_signature(self, zip_path: Path):
        """移除现有签名"""
        try:
            # 读取所有非签名文件
            files_to_keep = []
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                for file_info in zip_ref.filelist:
                    if not file_info.filename.startswith("META-INF/"):
                        files_to_keep.append((file_info.filename, zip_ref.read(file_info.filename)))
            
            # 重新创建ZIP文件
            with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zip_ref:
                for filename, data in files_to_keep:
                    zip_ref.writestr(filename, data)
                    
        except Exception as e:
            self.log(f"❌ 移除现有签名失败: {str(e)}")
            raise
    
    def compare_signatures(self, zip1_path: Path, zip2_path: Path) -> Dict:
        """比较两个ZIP文件的签名"""
        self.log(f"🔍 比较签名: {zip1_path.name} vs {zip2_path.name}")
        
        comparison_result = {
            'zip1_signatures': {},
            'zip2_signatures': {},
            'differences': [],
            'similarities': [],
            'match_percentage': 0
        }
        
        try:
            # 提取两个文件的签名信息
            for zip_path, key in [(zip1_path, 'zip1_signatures'), (zip2_path, 'zip2_signatures')]:
                with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                    signature_info = {}
                    
                    for file_name in zip_ref.namelist():
                        if file_name.startswith("META-INF/"):
                            file_data = zip_ref.read(file_name)
                            signature_info[file_name] = {
                                'size': len(file_data),
                                'sha256': hashlib.sha256(file_data).hexdigest()
                            }
                    
                    comparison_result[key] = signature_info
            
            # 比较签名文件
            zip1_files = set(comparison_result['zip1_signatures'].keys())
            zip2_files = set(comparison_result['zip2_signatures'].keys())
            
            # 找出差异
            only_in_zip1 = zip1_files - zip2_files
            only_in_zip2 = zip2_files - zip1_files
            common_files = zip1_files & zip2_files
            
            if only_in_zip1:
                comparison_result['differences'].append(f"仅在 {zip1_path.name} 中: {list(only_in_zip1)}")
            
            if only_in_zip2:
                comparison_result['differences'].append(f"仅在 {zip2_path.name} 中: {list(only_in_zip2)}")
            
            # 比较共同文件
            matching_files = 0
            for file_name in common_files:
                zip1_info = comparison_result['zip1_signatures'][file_name]
                zip2_info = comparison_result['zip2_signatures'][file_name]
                
                if zip1_info['sha256'] == zip2_info['sha256']:
                    comparison_result['similarities'].append(f"{file_name}: 完全匹配")
                    matching_files += 1
                else:
                    comparison_result['differences'].append(
                        f"{file_name}: 哈希不匹配 (大小: {zip1_info['size']} vs {zip2_info['size']})"
                    )
            
            # 计算匹配百分比
            total_files = len(zip1_files | zip2_files)
            if total_files > 0:
                comparison_result['match_percentage'] = (matching_files / total_files) * 100
            
            return comparison_result
            
        except Exception as e:
            self.log(f"❌ 签名比较失败: {str(e)}")
            return comparison_result
    
    def print_comparison_result(self, result: Dict):
        """打印比较结果"""
        print("\n" + "="*60)
        print("签名比较结果")
        print("="*60)
        
        print(f"\n📊 匹配度: {result['match_percentage']:.1f}%")
        
        if result['similarities']:
            print(f"\n✅ 相同的签名文件 ({len(result['similarities'])}):")
            for similarity in result['similarities']:
                print(f"  {similarity}")
        
        if result['differences']:
            print(f"\n❌ 不同的签名文件 ({len(result['differences'])}):")
            for difference in result['differences']:
                print(f"  {difference}")
        
        if result['match_percentage'] == 100:
            print("\n🎉 签名完全匹配！")
        elif result['match_percentage'] >= 80:
            print("\n✅ 签名基本匹配，应该可以正常使用")
        else:
            print("\n⚠️ 签名差异较大，可能需要进一步调整")
    
    def clone_and_compare(self) -> bool:
        """执行完整的克隆和比较流程"""
        self.log("🚀 开始签名克隆和比较流程...")
        
        # 检查文件存在性
        if not Path(self.original_zip).exists():
            self.log(f"❌ 找不到原始文件: {self.original_zip}")
            return False
        
        if not Path(self.target_zip).exists():
            self.log(f"❌ 找不到目标文件: {self.target_zip}")
            return False
        
        # 1. 提取原始签名
        signature_info = self.extract_original_signature()
        if not signature_info:
            return False
        
        # 2. 创建签名后的目标文件
        cloned_zip = f"cloned_signed_{self.target_zip}"
        cloned_path = Path(cloned_zip)
        
        # 复制目标文件
        shutil.copy2(self.target_zip, cloned_path)
        
        # 3. 应用克隆的签名
        if not self.apply_cloned_signature(cloned_path, signature_info):
            return False
        
        # 4. 比较签名
        comparison_result = self.compare_signatures(Path(self.original_zip), cloned_path)
        self.print_comparison_result(comparison_result)
        
        # 5. 计算文件哈希
        self.log("\n📊 文件信息:")
        for file_path in [Path(self.original_zip), cloned_path]:
            if file_path.exists():
                file_hash = self.calculate_file_hash(file_path)
                self.log(f"  {file_path.name}:")
                self.log(f"    大小: {file_path.stat().st_size:,} 字节")
                self.log(f"    SHA256: {file_hash['sha256']}")
        
        self.log(f"\n✅ 克隆签名完成: {cloned_zip}")
        return True
    
    def calculate_file_hash(self, file_path: Path) -> Dict[str, str]:
        """计算文件哈希"""
        hash_sha256 = hashlib.sha256()
        
        with open(file_path, 'rb') as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_sha256.update(chunk)
        
        return {'sha256': hash_sha256.hexdigest()}

def main():
    """主函数"""
    print("🔧 签名克隆工具")
    print("从 update.zip 克隆签名到 update_diy.zip")
    print("Copyright: By.举个🌰")
    print("-" * 50)
    
    # 创建克隆工具实例
    clone_tool = SignatureCloneTool()
    
    # 执行克隆和比较
    success = clone_tool.clone_and_compare()
    
    if success:
        print("\n🎉 签名克隆和比较完成!")
        print("现在可以使用 cloned_signed_update_diy.zip 进行刷机")
    else:
        print("\n❌ 签名克隆失败，请检查错误信息")

if __name__ == "__main__":
    main()
