#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单签名克隆工具
直接复制原始update.zip的签名到update_diy.zip

Author: By.举个🌰
Date: 2025
Copyright: By.举个🌰
"""

import os
import sys
import hashlib
import binascii
import zipfile
import shutil
from pathlib import Path
from typing import Dict, List, Optional
import tempfile
import datetime

class SimpleSignatureClone:
    """简单签名克隆工具"""
    
    def __init__(self):
        self.work_dir = Path(".")
        self.original_zip = "update.zip"
        self.target_zip = "update_diy.zip"
        
    def log(self, message: str):
        """输出日志"""
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        print(f"[{timestamp}] {message}")
    
    def extract_signature_files(self) -> Optional[Dict]:
        """提取原始签名文件"""
        self.log("🔍 提取原始签名文件...")
        
        original_path = Path(self.original_zip)
        if not original_path.exists():
            self.log(f"❌ 找不到原始文件: {self.original_zip}")
            return None
        
        signature_files = {}
        
        try:
            with zipfile.ZipFile(original_path, 'r') as zip_ref:
                for file_name in zip_ref.namelist():
                    if file_name.startswith("META-INF/"):
                        file_data = zip_ref.read(file_name)
                        signature_files[file_name] = file_data
                        self.log(f"✅ 提取 {file_name} ({len(file_data)} 字节)")
                
                if not signature_files:
                    self.log("❌ 原始文件没有签名")
                    return None
                
                self.log(f"✅ 提取了 {len(signature_files)} 个签名文件")
                return signature_files
                
        except Exception as e:
            self.log(f"❌ 提取签名失败: {str(e)}")
            return None
    
    def create_target_manifest(self, target_zip_path: Path) -> str:
        """为目标文件创建MANIFEST.MF"""
        self.log("📋 为目标文件创建MANIFEST.MF...")
        
        manifest_lines = [
            "Manifest-Version: 1.0",
            "Created-By: 1.0 (Android SignApk)",
            ""
        ]
        
        try:
            with zipfile.ZipFile(target_zip_path, 'r') as zip_ref:
                file_count = 0
                for file_info in zip_ref.filelist:
                    # 跳过META-INF目录和目录条目
                    if file_info.filename.startswith("META-INF/") or file_info.filename.endswith("/"):
                        continue
                    
                    # 读取文件内容并计算哈希
                    file_data = zip_ref.read(file_info.filename)
                    
                    # 计算SHA1哈希
                    sha1_hash = hashlib.sha1(file_data).digest()
                    sha1_b64 = binascii.b2a_base64(sha1_hash).decode().strip()
                    
                    # 添加到清单
                    manifest_lines.extend([
                        f"Name: {file_info.filename}",
                        f"SHA1-Digest: {sha1_b64}",
                        ""
                    ])
                    file_count += 1
            
            manifest_content = "\n".join(manifest_lines)
            self.log(f"✅ MANIFEST.MF创建完成，包含 {file_count} 个文件")
            return manifest_content
            
        except Exception as e:
            self.log(f"❌ 创建MANIFEST.MF失败: {str(e)}")
            return ""
    
    def create_target_cert_sf(self, manifest_content: str) -> str:
        """创建CERT.SF文件"""
        self.log("🔐 创建CERT.SF...")
        
        # 计算整个清单的哈希
        manifest_bytes = manifest_content.encode('utf-8')
        manifest_sha1 = hashlib.sha1(manifest_bytes).digest()
        manifest_sha1_b64 = binascii.b2a_base64(manifest_sha1).decode().strip()
        
        cert_sf_lines = [
            "Signature-Version: 1.0",
            "Created-By: 1.0 (Android SignApk)",
            f"SHA1-Digest-Manifest: {manifest_sha1_b64}",
            ""
        ]
        
        # 为每个文件条目创建签名条目
        manifest_lines = manifest_content.split('\n')
        i = 0
        while i < len(manifest_lines):
            line = manifest_lines[i]
            if line.startswith("Name: "):
                # 收集这个文件条目的所有行
                entry_lines = [line]
                i += 1
                while i < len(manifest_lines) and manifest_lines[i].strip() and not manifest_lines[i].startswith("Name: "):
                    entry_lines.append(manifest_lines[i])
                    i += 1
                
                # 计算条目的哈希
                entry_content = '\n'.join(entry_lines) + '\n'
                entry_sha1 = hashlib.sha1(entry_content.encode()).digest()
                entry_sha1_b64 = binascii.b2a_base64(entry_sha1).decode().strip()
                
                # 添加到CERT.SF
                cert_sf_lines.extend([
                    entry_lines[0],  # Name: 行
                    f"SHA1-Digest: {entry_sha1_b64}",
                    ""
                ])
            else:
                i += 1
        
        cert_sf_content = "\n".join(cert_sf_lines)
        self.log("✅ CERT.SF创建完成")
        return cert_sf_content
    
    def apply_cloned_signature(self, target_zip_path: Path, signature_files: Dict) -> bool:
        """应用克隆的签名"""
        self.log(f"✍️ 应用克隆签名到 {target_zip_path.name}...")
        
        try:
            with tempfile.TemporaryDirectory() as temp_dir:
                temp_path = Path(temp_dir)
                temp_zip = temp_path / "temp.zip"
                
                # 复制目标文件
                shutil.copy2(target_zip_path, temp_zip)
                
                # 移除现有签名
                self.remove_existing_signature(temp_zip)
                
                # 创建新的MANIFEST.MF和CERT.SF
                manifest_content = self.create_target_manifest(temp_zip)
                cert_sf_content = self.create_target_cert_sf(manifest_content)
                
                # 创建META-INF目录
                meta_inf_dir = temp_path / "META-INF"
                meta_inf_dir.mkdir(exist_ok=True)
                
                # 写入新的MANIFEST.MF和CERT.SF
                manifest_path = meta_inf_dir / "MANIFEST.MF"
                with open(manifest_path, 'w', encoding='utf-8') as f:
                    f.write(manifest_content)
                
                cert_sf_path = meta_inf_dir / "CERT.SF"
                with open(cert_sf_path, 'w', encoding='utf-8') as f:
                    f.write(cert_sf_content)
                
                # 复制原始的其他签名文件（证书等）
                for file_name, file_data in signature_files.items():
                    if file_name not in ["META-INF/MANIFEST.MF", "META-INF/CERT.SF"]:
                        file_path = meta_inf_dir / Path(file_name).name
                        with open(file_path, 'wb') as f:
                            f.write(file_data)
                
                # 将所有签名文件添加到ZIP
                with zipfile.ZipFile(temp_zip, 'a') as zip_ref:
                    # 添加MANIFEST.MF和CERT.SF
                    zip_ref.write(manifest_path, "META-INF/MANIFEST.MF")
                    zip_ref.write(cert_sf_path, "META-INF/CERT.SF")
                    
                    # 添加其他签名文件
                    for file_name in signature_files.keys():
                        if file_name not in ["META-INF/MANIFEST.MF", "META-INF/CERT.SF"]:
                            file_path = meta_inf_dir / Path(file_name).name
                            if file_path.exists():
                                zip_ref.write(file_path, file_name)
                
                # 替换原文件
                shutil.copy2(temp_zip, target_zip_path)
                
            self.log("✅ 克隆签名应用完成")
            return True
            
        except Exception as e:
            self.log(f"❌ 应用签名失败: {str(e)}")
            return False
    
    def remove_existing_signature(self, zip_path: Path):
        """移除现有签名"""
        try:
            # 读取所有非签名文件
            files_to_keep = []
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                for file_info in zip_ref.filelist:
                    if not file_info.filename.startswith("META-INF/"):
                        files_to_keep.append((file_info.filename, zip_ref.read(file_info.filename)))
            
            # 重新创建ZIP文件
            with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zip_ref:
                for filename, data in files_to_keep:
                    zip_ref.writestr(filename, data)
                    
        except Exception as e:
            self.log(f"❌ 移除现有签名失败: {str(e)}")
            raise
    
    def compare_signatures(self, original_path: Path, cloned_path: Path):
        """比较签名"""
        self.log("🔍 比较签名结构...")
        
        print("\n" + "="*60)
        print("签名比较结果")
        print("="*60)
        
        try:
            original_meta = {}
            cloned_meta = {}
            
            # 提取原始文件的META-INF信息
            with zipfile.ZipFile(original_path, 'r') as zip_ref:
                for file_name in zip_ref.namelist():
                    if file_name.startswith("META-INF/"):
                        file_data = zip_ref.read(file_name)
                        original_meta[file_name] = {
                            'size': len(file_data),
                            'sha256': hashlib.sha256(file_data).hexdigest()
                        }
            
            # 提取克隆文件的META-INF信息
            with zipfile.ZipFile(cloned_path, 'r') as zip_ref:
                for file_name in zip_ref.namelist():
                    if file_name.startswith("META-INF/"):
                        file_data = zip_ref.read(file_name)
                        cloned_meta[file_name] = {
                            'size': len(file_data),
                            'sha256': hashlib.sha256(file_data).hexdigest()
                        }
            
            # 比较结果
            all_files = set(original_meta.keys()) | set(cloned_meta.keys())
            matching_files = 0
            
            print(f"\n📊 签名文件对比:")
            for file_name in sorted(all_files):
                if file_name in original_meta and file_name in cloned_meta:
                    orig_info = original_meta[file_name]
                    clone_info = cloned_meta[file_name]
                    
                    if file_name in ["META-INF/MANIFEST.MF", "META-INF/CERT.SF"]:
                        # 这两个文件内容会不同，但结构应该相同
                        print(f"  📝 {file_name}: 重新生成 (大小: {orig_info['size']} -> {clone_info['size']})")
                    elif orig_info['sha256'] == clone_info['sha256']:
                        print(f"  ✅ {file_name}: 完全匹配")
                        matching_files += 1
                    else:
                        print(f"  ❌ {file_name}: 不匹配")
                elif file_name in original_meta:
                    print(f"  ⚠️ {file_name}: 仅在原始文件中")
                else:
                    print(f"  ⚠️ {file_name}: 仅在克隆文件中")
            
            # 检查关键文件是否存在
            key_files = ["META-INF/MANIFEST.MF", "META-INF/CERT.SF", "META-INF/CERT.RSA"]
            all_key_files_present = all(f in cloned_meta for f in key_files)
            
            print(f"\n📈 关键签名文件: {'✅ 完整' if all_key_files_present else '❌ 缺失'}")
            
            if all_key_files_present:
                print("🎉 签名结构完整，应该可以正常使用！")
            else:
                print("⚠️ 签名结构不完整，可能无法通过验证")
                
        except Exception as e:
            print(f"❌ 比较失败: {str(e)}")
    
    def clone_signature(self) -> bool:
        """执行签名克隆"""
        self.log("🚀 开始签名克隆...")
        
        # 检查文件存在性
        if not Path(self.original_zip).exists():
            self.log(f"❌ 找不到原始文件: {self.original_zip}")
            return False
        
        if not Path(self.target_zip).exists():
            self.log(f"❌ 找不到目标文件: {self.target_zip}")
            return False
        
        # 1. 提取原始签名文件
        signature_files = self.extract_signature_files()
        if not signature_files:
            return False
        
        # 2. 创建克隆文件
        cloned_zip = f"final_signed_{self.target_zip}"
        cloned_path = Path(cloned_zip)
        
        # 复制目标文件
        shutil.copy2(self.target_zip, cloned_path)
        
        # 3. 应用克隆签名
        if not self.apply_cloned_signature(cloned_path, signature_files):
            return False
        
        # 4. 比较签名
        self.compare_signatures(Path(self.original_zip), cloned_path)
        
        # 5. 显示文件信息
        self.log("\n📊 文件信息:")
        for file_path in [Path(self.original_zip), cloned_path]:
            if file_path.exists():
                file_size = file_path.stat().st_size
                self.log(f"  {file_path.name}: {file_size:,} 字节")
        
        self.log(f"\n✅ 签名克隆完成: {cloned_zip}")
        return True

def main():
    """主函数"""
    print("🔧 简单签名克隆工具")
    print("从 update.zip 克隆签名到 update_diy.zip")
    print("Copyright: By.举个🌰")
    print("-" * 50)
    
    # 创建克隆工具实例
    clone_tool = SimpleSignatureClone()
    
    # 执行签名克隆
    success = clone_tool.clone_signature()
    
    if success:
        print("\n🎉 签名克隆完成!")
        print("现在可以使用 final_signed_update_diy.zip 进行刷机")
        print("这个文件使用了与原始 update.zip 相同的签名结构")
    else:
        print("\n❌ 签名克隆失败，请检查错误信息")

if __name__ == "__main__":
    main()
