@echo off
echo ========================================
echo 晶晨机顶盒固件逆向分析工具启动器
echo By.举个🌰
echo ========================================
echo.

REM 检查虚拟环境是否存在
if not exist "firmware_analysis_env\Scripts\activate.bat" (
    echo 创建虚拟环境...
    python -m venv firmware_analysis_env
    echo 虚拟环境创建完成
    echo.
)

REM 激活虚拟环境
echo 激活虚拟环境...
call firmware_analysis_env\Scripts\activate.bat

REM 安装依赖
echo 检查并安装依赖...
pip install cryptography >nul 2>&1

echo.
echo ========================================
echo 选择要运行的分析工具:
echo ========================================
echo 1. 固件基础分析 (firmware_reverse_assistant.py)
echo 2. ADB调试分析 (adb_debug_analyzer.py)  
echo 3. 签名机制深度分析 (signature_analysis_toolkit.py)
echo 4. 运行所有分析工具
echo 5. 退出
echo ========================================
echo.

set /p choice=请输入选择 (1-5): 

if "%choice%"=="1" (
    echo.
    echo 运行固件基础分析...
    python firmware_reverse_assistant.py
    pause
) else if "%choice%"=="2" (
    echo.
    echo 运行ADB调试分析...
    python adb_debug_analyzer.py
    pause
) else if "%choice%"=="3" (
    echo.
    echo 运行签名机制深度分析...
    python signature_analysis_toolkit.py
    pause
) else if "%choice%"=="4" (
    echo.
    echo 运行所有分析工具...
    echo.
    echo [1/3] 固件基础分析...
    python firmware_reverse_assistant.py
    echo.
    echo [2/3] ADB调试分析...
    python adb_debug_analyzer.py
    echo.
    echo [3/3] 签名机制深度分析...
    python signature_analysis_toolkit.py
    echo.
    echo 所有分析完成！
    pause
) else if "%choice%"=="5" (
    echo 退出...
    exit /b 0
) else (
    echo 无效选择，请重新运行脚本
    pause
)

REM 保持虚拟环境激活状态
echo.
echo 虚拟环境仍处于激活状态，你可以继续使用Python工具
echo 要退出虚拟环境，请输入: deactivate
cmd /k
