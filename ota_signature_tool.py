#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OTA签名工具 - 图形界面版本
用于对晶晨机顶盒卡刷包进行签名操作

Author: By.举个🌰
Date: 2025
Copyright: By.举个🌰
"""

import os
import sys
import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
import zipfile
import hashlib
import binascii
from pathlib import Path
from typing import Optional, Dict, List
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric import rsa, padding
from cryptography import x509
from cryptography.x509.oid import NameOID
import datetime

class OTASignatureTool:
    """OTA签名工具主类"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("OTA签名工具 - By.举个🌰")
        self.root.geometry("800x600")
        self.root.resizable(True, True)
        
        # 设置图标和样式
        self.setup_styles()
        
        # 初始化变量
        self.selected_ota_file = tk.StringVar()
        self.output_directory = tk.StringVar(value=str(Path.cwd()))
        self.signature_method = tk.StringVar(value="RSA-2048")
        self.progress_var = tk.DoubleVar()
        
        # 创建界面
        self.create_widgets()
        
        # 日志缓冲区
        self.log_buffer = []
        
    def setup_styles(self):
        """设置界面样式"""
        style = ttk.Style()
        style.theme_use('clam')
        
        # 配置颜色主题
        style.configure('Title.TLabel', font=('Arial', 14, 'bold'), foreground='#2E86AB')
        style.configure('Header.TLabel', font=('Arial', 10, 'bold'), foreground='#A23B72')
        style.configure('Success.TLabel', foreground='#28A745')
        style.configure('Error.TLabel', foreground='#DC3545')
        
    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="🔐 OTA签名工具", style='Title.TLabel')
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # 文件选择区域
        self.create_file_selection_area(main_frame, 1)
        
        # 签名配置区域
        self.create_signature_config_area(main_frame, 2)
        
        # 操作按钮区域
        self.create_action_buttons_area(main_frame, 3)
        
        # 进度条
        self.create_progress_area(main_frame, 4)
        
        # 日志输出区域
        self.create_log_area(main_frame, 5)
        
        # 状态栏
        self.create_status_bar(main_frame, 6)
        
    def create_file_selection_area(self, parent, row):
        """创建文件选择区域"""
        # 文件选择框架
        file_frame = ttk.LabelFrame(parent, text="📁 文件选择", padding="10")
        file_frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        file_frame.columnconfigure(1, weight=1)
        
        # OTA文件选择
        ttk.Label(file_frame, text="卡刷包文件:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        ttk.Entry(file_frame, textvariable=self.selected_ota_file, state='readonly').grid(
            row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        ttk.Button(file_frame, text="浏览", command=self.select_ota_file).grid(row=0, column=2)
        
        # 输出目录选择
        ttk.Label(file_frame, text="输出目录:").grid(row=1, column=0, sticky=tk.W, padx=(0, 10), pady=(10, 0))
        ttk.Entry(file_frame, textvariable=self.output_directory, state='readonly').grid(
            row=1, column=1, sticky=(tk.W, tk.E), padx=(0, 10), pady=(10, 0))
        ttk.Button(file_frame, text="浏览", command=self.select_output_directory).grid(
            row=1, column=2, pady=(10, 0))
    
    def create_signature_config_area(self, parent, row):
        """创建签名配置区域"""
        config_frame = ttk.LabelFrame(parent, text="⚙️ 签名配置", padding="10")
        config_frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        config_frame.columnconfigure(1, weight=1)
        
        # 签名方法选择
        ttk.Label(config_frame, text="签名算法:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        method_combo = ttk.Combobox(config_frame, textvariable=self.signature_method, 
                                   values=["RSA-2048", "RSA-4096"], state='readonly')
        method_combo.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        method_combo.set("RSA-2048")
        
        # 签名选项
        self.use_existing_keys = tk.BooleanVar(value=False)
        self.generate_new_keys = tk.BooleanVar(value=True)
        
        ttk.Checkbutton(config_frame, text="使用现有密钥", variable=self.use_existing_keys,
                       command=self.toggle_key_options).grid(row=1, column=0, sticky=tk.W, pady=(10, 0))
        ttk.Checkbutton(config_frame, text="生成新密钥", variable=self.generate_new_keys,
                       command=self.toggle_key_options).grid(row=1, column=1, sticky=tk.W, pady=(10, 0))
    
    def create_action_buttons_area(self, parent, row):
        """创建操作按钮区域"""
        button_frame = ttk.Frame(parent)
        button_frame.grid(row=row, column=0, columnspan=3, pady=(0, 10))
        
        # 按钮样式
        button_style = {'width': 15, 'padding': 5}
        
        ttk.Button(button_frame, text="🔍 分析卡刷包", command=self.analyze_ota_package, 
                  **button_style).grid(row=0, column=0, padx=5)
        ttk.Button(button_frame, text="🔑 生成密钥", command=self.generate_keys,
                  **button_style).grid(row=0, column=1, padx=5)
        ttk.Button(button_frame, text="✍️ 签名卡刷包", command=self.sign_ota_package,
                  **button_style).grid(row=0, column=2, padx=5)
        ttk.Button(button_frame, text="🧹 清除日志", command=self.clear_log,
                  **button_style).grid(row=0, column=3, padx=5)
    
    def create_progress_area(self, parent, row):
        """创建进度条区域"""
        progress_frame = ttk.Frame(parent)
        progress_frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        progress_frame.columnconfigure(0, weight=1)
        
        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress_var, 
                                          maximum=100, mode='determinate')
        self.progress_bar.grid(row=0, column=0, sticky=(tk.W, tk.E))
        
        self.progress_label = ttk.Label(progress_frame, text="就绪")
        self.progress_label.grid(row=1, column=0, pady=(5, 0))
    
    def create_log_area(self, parent, row):
        """创建日志输出区域"""
        log_frame = ttk.LabelFrame(parent, text="📋 操作日志", padding="5")
        log_frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=15, wrap=tk.WORD)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        parent.rowconfigure(row, weight=1)
    
    def create_status_bar(self, parent, row):
        """创建状态栏"""
        status_frame = ttk.Frame(parent)
        status_frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E))
        status_frame.columnconfigure(1, weight=1)
        
        self.status_label = ttk.Label(status_frame, text="就绪", relief=tk.SUNKEN)
        self.status_label.grid(row=0, column=0, sticky=(tk.W, tk.E))
        
        # 版权信息
        copyright_label = ttk.Label(status_frame, text="Copyright © 2025 By.举个🌰", 
                                   font=('Arial', 8))
        copyright_label.grid(row=0, column=1, sticky=tk.E)
    
    def toggle_key_options(self):
        """切换密钥选项"""
        if self.use_existing_keys.get():
            self.generate_new_keys.set(False)
        elif self.generate_new_keys.get():
            self.use_existing_keys.set(False)
    
    def select_ota_file(self):
        """选择OTA文件"""
        file_path = filedialog.askopenfilename(
            title="选择卡刷包文件",
            filetypes=[("ZIP文件", "*.zip"), ("所有文件", "*.*")]
        )
        if file_path:
            self.selected_ota_file.set(file_path)
            self.log(f"✅ 已选择卡刷包: {Path(file_path).name}")
    
    def select_output_directory(self):
        """选择输出目录"""
        directory = filedialog.askdirectory(title="选择输出目录")
        if directory:
            self.output_directory.set(directory)
            self.log(f"✅ 已选择输出目录: {directory}")
    
    def log(self, message: str):
        """添加日志消息"""
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}\n"
        
        self.log_text.insert(tk.END, log_message)
        self.log_text.see(tk.END)
        self.root.update_idletasks()
    
    def clear_log(self):
        """清除日志"""
        self.log_text.delete(1.0, tk.END)
        self.log("🧹 日志已清除")
    
    def update_progress(self, value: float, text: str = ""):
        """更新进度条"""
        self.progress_var.set(value)
        if text:
            self.progress_label.config(text=text)
        self.root.update_idletasks()
    
    def update_status(self, message: str):
        """更新状态栏"""
        self.status_label.config(text=message)
        self.root.update_idletasks()
    
    def analyze_ota_package(self):
        """分析OTA包"""
        if not self.selected_ota_file.get():
            messagebox.showerror("错误", "请先选择卡刷包文件")
            return
        
        def analyze_thread():
            try:
                self.update_status("正在分析卡刷包...")
                self.update_progress(10, "正在读取文件...")
                
                ota_path = Path(self.selected_ota_file.get())
                self.log(f"🔍 开始分析卡刷包: {ota_path.name}")
                
                # 检查文件
                if not ota_path.exists():
                    raise FileNotFoundError("卡刷包文件不存在")
                
                self.update_progress(30, "正在解析ZIP结构...")
                
                # 分析ZIP文件
                with zipfile.ZipFile(ota_path, 'r') as zip_ref:
                    file_list = zip_ref.namelist()
                    self.log(f"📦 ZIP文件包含 {len(file_list)} 个文件")
                    
                    # 查找关键文件
                    key_files = []
                    for file_name in file_list:
                        if any(keyword in file_name.lower() for keyword in 
                              ['system', 'boot', 'recovery', 'meta-inf', 'update-binary']):
                            key_files.append(file_name)
                    
                    self.update_progress(60, "正在分析关键文件...")
                    
                    if key_files:
                        self.log("🔑 发现关键文件:")
                        for key_file in key_files[:10]:  # 显示前10个
                            self.log(f"  - {key_file}")
                    
                    # 检查签名信息
                    meta_inf_files = [f for f in file_list if 'META-INF' in f.upper()]
                    if meta_inf_files:
                        self.log("📜 发现签名相关文件:")
                        for meta_file in meta_inf_files:
                            self.log(f"  - {meta_file}")
                
                self.update_progress(90, "正在计算文件哈希...")
                
                # 计算文件哈希
                file_hash = self.calculate_file_hash(ota_path)
                self.log(f"🔐 文件哈希值:")
                self.log(f"  MD5: {file_hash['md5']}")
                self.log(f"  SHA256: {file_hash['sha256']}")
                
                self.update_progress(100, "分析完成")
                self.log("✅ 卡刷包分析完成")
                self.update_status("分析完成")
                
            except Exception as e:
                self.log(f"❌ 分析失败: {str(e)}")
                self.update_status("分析失败")
                messagebox.showerror("分析失败", f"分析卡刷包时发生错误:\n{str(e)}")
            finally:
                self.update_progress(0, "就绪")
        
        # 在新线程中运行分析
        threading.Thread(target=analyze_thread, daemon=True).start()
    
    def calculate_file_hash(self, file_path: Path) -> Dict[str, str]:
        """计算文件哈希值"""
        hash_md5 = hashlib.md5()
        hash_sha256 = hashlib.sha256()
        
        with open(file_path, 'rb') as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
                hash_sha256.update(chunk)
        
        return {
            'md5': hash_md5.hexdigest(),
            'sha256': hash_sha256.hexdigest()
        }
    
    def generate_keys(self):
        """生成签名密钥"""
        def generate_thread():
            try:
                self.update_status("正在生成密钥...")
                self.update_progress(10, "正在生成RSA密钥对...")
                self.log("🔑 开始生成签名密钥...")
                
                # 确定密钥长度
                key_size = 2048 if self.signature_method.get() == "RSA-2048" else 4096
                self.log(f"📏 密钥长度: {key_size} bits")
                
                self.update_progress(30, f"正在生成{key_size}位RSA密钥...")
                
                # 生成私钥
                private_key = rsa.generate_private_key(
                    public_exponent=65537,
                    key_size=key_size
                )
                
                self.update_progress(60, "正在生成证书...")
                
                # 生成自签名证书
                subject = issuer = x509.Name([
                    x509.NameAttribute(NameOID.COUNTRY_NAME, "CN"),
                    x509.NameAttribute(NameOID.STATE_OR_PROVINCE_NAME, "GuangDong"),
                    x509.NameAttribute(NameOID.LOCALITY_NAME, "ShenZhen"),
                    x509.NameAttribute(NameOID.ORGANIZATION_NAME, "Custom OTA"),
                    x509.NameAttribute(NameOID.COMMON_NAME, "OTA Signing Key"),
                ])
                
                cert = x509.CertificateBuilder().subject_name(
                    subject
                ).issuer_name(
                    issuer
                ).public_key(
                    private_key.public_key()
                ).serial_number(
                    x509.random_serial_number()
                ).not_valid_before(
                    datetime.datetime.utcnow()
                ).not_valid_after(
                    datetime.datetime.utcnow() + datetime.timedelta(days=3650)  # 10年有效期
                ).sign(private_key, hashes.SHA256())
                
                self.update_progress(80, "正在保存密钥文件...")
                
                # 保存密钥和证书
                output_dir = Path(self.output_directory.get())
                
                # 保存私钥
                private_key_path = output_dir / "ota_private_key.pem"
                with open(private_key_path, 'wb') as f:
                    f.write(private_key.private_bytes(
                        encoding=serialization.Encoding.PEM,
                        format=serialization.PrivateFormat.PKCS8,
                        encryption_algorithm=serialization.NoEncryption()
                    ))
                
                # 保存证书
                cert_path = output_dir / "ota_certificate.pem"
                with open(cert_path, 'wb') as f:
                    f.write(cert.public_bytes(serialization.Encoding.PEM))
                
                # 保存公钥(十六进制格式)
                public_key = private_key.public_key()
                public_key_der = public_key.public_bytes(
                    encoding=serialization.Encoding.DER,
                    format=serialization.PublicFormat.SubjectPublicKeyInfo
                )
                
                pubkey_hex_path = output_dir / "ota_public_key.txt"
                with open(pubkey_hex_path, 'w') as f:
                    f.write(binascii.hexlify(public_key_der).decode('utf-8'))
                
                self.update_progress(100, "密钥生成完成")
                
                self.log("✅ 密钥生成完成:")
                self.log(f"  🔐 私钥: {private_key_path}")
                self.log(f"  📜 证书: {cert_path}")
                self.log(f"  🔑 公钥: {pubkey_hex_path}")
                
                self.update_status("密钥生成完成")
                messagebox.showinfo("成功", "签名密钥生成完成！")
                
            except Exception as e:
                self.log(f"❌ 密钥生成失败: {str(e)}")
                self.update_status("密钥生成失败")
                messagebox.showerror("生成失败", f"生成密钥时发生错误:\n{str(e)}")
            finally:
                self.update_progress(0, "就绪")
        
        # 在新线程中运行生成
        threading.Thread(target=generate_thread, daemon=True).start()
    
    def sign_ota_package(self):
        """签名OTA包"""
        if not self.selected_ota_file.get():
            messagebox.showerror("错误", "请先选择卡刷包文件")
            return
        
        def sign_thread():
            try:
                self.update_status("正在签名卡刷包...")
                self.update_progress(10, "正在准备签名...")
                
                ota_path = Path(self.selected_ota_file.get())
                output_dir = Path(self.output_directory.get())
                
                self.log(f"✍️ 开始签名卡刷包: {ota_path.name}")
                
                # 检查密钥文件
                private_key_path = output_dir / "ota_private_key.pem"
                cert_path = output_dir / "ota_certificate.pem"
                
                if not private_key_path.exists() or not cert_path.exists():
                    raise FileNotFoundError("找不到签名密钥文件，请先生成密钥")
                
                self.update_progress(30, "正在加载签名密钥...")
                
                # 加载私钥
                with open(private_key_path, 'rb') as f:
                    private_key = serialization.load_pem_private_key(f.read(), password=None)
                
                # 加载证书
                with open(cert_path, 'rb') as f:
                    certificate = x509.load_pem_x509_certificate(f.read())
                
                self.update_progress(50, "正在创建签名包...")
                
                # 创建签名后的文件名
                signed_ota_path = output_dir / f"signed_{ota_path.name}"
                
                # 复制原始文件
                import shutil
                shutil.copy2(ota_path, signed_ota_path)
                
                self.update_progress(70, "正在添加签名信息...")
                
                # 实现OTA签名逻辑
                self.log("📝 正在添加签名信息到ZIP文件...")

                # 添加签名到ZIP文件
                self.add_signature_to_zip(signed_ota_path, private_key, certificate)

                # 计算签名后文件的哈希
                signed_hash = self.calculate_file_hash(signed_ota_path)
                
                self.update_progress(90, "正在保存签名信息...")
                
                # 保存签名信息
                signature_info_path = output_dir / f"signature_info_{ota_path.stem}.txt"
                with open(signature_info_path, 'w', encoding='utf-8') as f:
                    f.write(f"OTA签名信息\n")
                    f.write(f"原始文件: {ota_path.name}\n")
                    f.write(f"签名文件: {signed_ota_path.name}\n")
                    f.write(f"签名时间: {datetime.datetime.now().isoformat()}\n")
                    f.write(f"签名算法: {self.signature_method.get()}\n")
                    f.write(f"文件哈希: {signed_hash['sha256']}\n")
                    f.write(f"By.举个🌰\n")
                
                self.update_progress(100, "签名完成")
                
                self.log("✅ OTA包签名完成:")
                self.log(f"  📦 签名文件: {signed_ota_path}")
                self.log(f"  📋 签名信息: {signature_info_path}")
                self.log(f"  🔐 文件哈希: {signed_hash['sha256']}")
                
                self.update_status("签名完成")
                messagebox.showinfo("成功", f"OTA包签名完成！\n签名文件: {signed_ota_path.name}")
                
            except Exception as e:
                self.log(f"❌ 签名失败: {str(e)}")
                self.update_status("签名失败")
                messagebox.showerror("签名失败", f"签名OTA包时发生错误:\n{str(e)}")
            finally:
                self.update_progress(0, "就绪")
        
        # 在新线程中运行签名
        threading.Thread(target=sign_thread, daemon=True).start()

    def add_signature_to_zip(self, zip_path: Path, private_key, certificate):
        """向ZIP文件添加签名"""
        try:
            # 创建META-INF目录和签名文件
            import tempfile
            import shutil

            with tempfile.TemporaryDirectory() as temp_dir:
                temp_zip = Path(temp_dir) / "temp.zip"
                shutil.copy2(zip_path, temp_zip)

                # 打开ZIP文件并添加签名信息
                with zipfile.ZipFile(temp_zip, 'a') as zip_ref:
                    # 添加证书文件
                    cert_data = certificate.public_bytes(serialization.Encoding.PEM)
                    zip_ref.writestr("META-INF/CERT.RSA", cert_data)

                    # 创建签名清单
                    manifest_content = self.create_manifest(zip_ref)
                    zip_ref.writestr("META-INF/MANIFEST.MF", manifest_content)

                    # 创建签名文件
                    signature_content = self.create_signature_file(manifest_content, private_key)
                    zip_ref.writestr("META-INF/CERT.SF", signature_content)

                # 替换原文件
                shutil.copy2(temp_zip, zip_path)

            self.log("✅ 签名信息已添加到ZIP文件")

        except Exception as e:
            self.log(f"❌ 添加签名失败: {str(e)}")
            raise

    def create_manifest(self, zip_ref) -> str:
        """创建签名清单"""
        manifest_lines = ["Manifest-Version: 1.0"]
        manifest_lines.append("Created-By: OTA Signature Tool - By.举个🌰")
        manifest_lines.append("")

        # 为每个文件创建哈希条目
        for file_info in zip_ref.filelist:
            if not file_info.filename.startswith("META-INF/"):
                file_data = zip_ref.read(file_info.filename)
                file_hash = hashlib.sha256(file_data).digest()
                file_hash_b64 = binascii.b2a_base64(file_hash).decode().strip()

                manifest_lines.append(f"Name: {file_info.filename}")
                manifest_lines.append(f"SHA-256-Digest: {file_hash_b64}")
                manifest_lines.append("")

        return "\n".join(manifest_lines)

    def create_signature_file(self, manifest_content: str, private_key) -> str:
        """创建签名文件"""
        # 计算清单的哈希
        manifest_hash = hashlib.sha256(manifest_content.encode()).digest()
        manifest_hash_b64 = binascii.b2a_base64(manifest_hash).decode().strip()

        # 创建签名文件内容
        signature_lines = ["Signature-Version: 1.0"]
        signature_lines.append("Created-By: OTA Signature Tool - By.举个🌰")
        signature_lines.append(f"SHA-256-Digest-Manifest: {manifest_hash_b64}")
        signature_lines.append("")

        return "\n".join(signature_lines)

    def run(self):
        """运行应用程序"""
        self.log("🚀 OTA签名工具启动完成")
        self.log("📖 使用说明:")
        self.log("  1. 选择要签名的卡刷包文件")
        self.log("  2. 选择输出目录")
        self.log("  3. 点击'生成密钥'创建签名密钥")
        self.log("  4. 点击'签名卡刷包'进行签名")
        self.log("=" * 50)
        
        self.root.mainloop()

def main():
    """主函数"""
    try:
        app = OTASignatureTool()
        app.run()
    except Exception as e:
        messagebox.showerror("启动失败", f"应用程序启动失败:\n{str(e)}")

if __name__ == "__main__":
    main()
