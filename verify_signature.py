#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
签名验证工具
用于验证官方签名的卡刷包

Author: By.举个🌰
Date: 2025
Copyright: By.举个🌰
"""

import zipfile
import hashlib
import binascii
from pathlib import Path
import datetime

def verify_signature(zip_file: str):
    """验证ZIP文件的签名"""
    print("🔍 验证签名工具")
    print("Copyright: By.举个🌰")
    print("-" * 50)
    
    zip_path = Path(zip_file)
    if not zip_path.exists():
        print(f"❌ 文件不存在: {zip_file}")
        return False
    
    print(f"📦 验证文件: {zip_path.name}")
    print(f"📏 文件大小: {zip_path.stat().st_size:,} 字节")
    
    try:
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            file_list = zip_ref.namelist()
            print(f"📁 文件总数: {len(file_list)}")
            
            # 检查签名文件
            signature_files = {
                'META-INF/MANIFEST.MF': False,
                'META-INF/CERT.SF': False,
                'META-INF/CERT.RSA': False
            }
            
            for file_name in file_list:
                if file_name in signature_files:
                    signature_files[file_name] = True
            
            print("\n🔐 签名文件检查:")
            all_present = True
            for sig_file, present in signature_files.items():
                status = "✅ 存在" if present else "❌ 缺失"
                print(f"  {sig_file}: {status}")
                if not present:
                    all_present = False
            
            if not all_present:
                print("\n❌ 签名文件不完整")
                return False
            
            # 读取并分析签名文件
            print("\n📋 分析签名文件:")
            
            # 分析MANIFEST.MF
            manifest_content = zip_ref.read('META-INF/MANIFEST.MF').decode('utf-8')
            manifest_lines = manifest_content.split('\n')
            
            file_entries = 0
            for line in manifest_lines:
                if line.startswith('Name: '):
                    file_entries += 1
            
            print(f"  MANIFEST.MF: {file_entries} 个文件条目")
            
            # 分析CERT.SF
            cert_sf_content = zip_ref.read('META-INF/CERT.SF').decode('utf-8')
            cert_sf_lines = cert_sf_content.split('\n')
            
            sf_entries = 0
            for line in cert_sf_lines:
                if line.startswith('Name: '):
                    sf_entries += 1
            
            print(f"  CERT.SF: {sf_entries} 个签名条目")
            
            # 分析CERT.RSA
            cert_rsa_data = zip_ref.read('META-INF/CERT.RSA')
            print(f"  CERT.RSA: {len(cert_rsa_data)} 字节证书数据")
            
            # 验证文件完整性
            print("\n🔍 验证文件完整性:")
            
            verified_files = 0
            total_files = 0
            
            for line in manifest_lines:
                if line.startswith('Name: '):
                    file_name = line[6:].strip()
                    total_files += 1
                    
                    if file_name in file_list:
                        verified_files += 1
                    else:
                        print(f"  ❌ 缺失文件: {file_name}")
            
            print(f"  验证文件: {verified_files}/{total_files}")
            
            if verified_files == total_files:
                print("  ✅ 所有文件完整")
            else:
                print("  ❌ 文件不完整")
            
            # 计算文件哈希
            print("\n🔐 文件哈希:")
            file_hash = calculate_file_hash(zip_path)
            print(f"  MD5:    {file_hash['md5']}")
            print(f"  SHA1:   {file_hash['sha1']}")
            print(f"  SHA256: {file_hash['sha256']}")
            
            print("\n✅ 签名验证完成")
            print("📝 签名结构正确，包含完整的官方签名信息")
            
            return True
            
    except Exception as e:
        print(f"❌ 验证失败: {str(e)}")
        return False

def calculate_file_hash(file_path: Path):
    """计算文件哈希"""
    hash_md5 = hashlib.md5()
    hash_sha1 = hashlib.sha1()
    hash_sha256 = hashlib.sha256()
    
    with open(file_path, 'rb') as f:
        for chunk in iter(lambda: f.read(4096), b""):
            hash_md5.update(chunk)
            hash_sha1.update(chunk)
            hash_sha256.update(chunk)
    
    return {
        'md5': hash_md5.hexdigest(),
        'sha1': hash_sha1.hexdigest(),
        'sha256': hash_sha256.hexdigest()
    }

def main():
    """主函数"""
    # 验证官方签名的文件
    signed_file = "official_signed_update_diy.zip"
    
    if Path(signed_file).exists():
        verify_signature(signed_file)
    else:
        print(f"❌ 找不到签名文件: {signed_file}")

if __name__ == "__main__":
    main()
