#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Error 21 修复工具
专门用于修复Android OTA包签名验证错误

Author: By.举个🌰
Date: 2025
Copyright: By.举个🌰
"""

import os
import sys
import hashlib
import binascii
import zipfile
import shutil
from pathlib import Path
from typing import Dict, List, Optional
import tempfile
import datetime

class Error21FixTool:
    """Error 21 修复工具"""
    
    def __init__(self, work_dir: str = "."):
        self.work_dir = Path(work_dir)
        self.system_dir = self.work_dir / "system"
        
    def log(self, message: str):
        """输出日志"""
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        print(f"[{timestamp}] {message}")
    
    def analyze_error21(self):
        """分析Error 21错误原因"""
        self.log("🔍 分析Error 21错误...")
        
        print("\n" + "="*60)
        print("Error 21 错误分析")
        print("="*60)
        
        print("\n📋 Error 21 常见原因:")
        print("1. 签名验证失败 - 证书不匹配设备公钥")
        print("2. 文件完整性校验失败 - 哈希值不匹配")
        print("3. 签名格式错误 - META-INF结构不正确")
        print("4. 证书链验证失败 - 证书格式问题")
        print("5. 设备型号不匹配 - 固件不适用当前设备")
        
        print("\n🔧 可能的解决方案:")
        print("1. 重新生成正确的签名文件")
        print("2. 使用设备原生的签名方式")
        print("3. 修复文件哈希计算方法")
        print("4. 调整签名文件格式")
        print("5. 绕过签名验证机制")
    
    def create_legacy_signature(self, zip_path: Path) -> bool:
        """创建传统格式的签名"""
        self.log("🔄 创建传统格式签名...")
        
        try:
            with tempfile.TemporaryDirectory() as temp_dir:
                temp_path = Path(temp_dir)
                temp_zip = temp_path / "temp.zip"
                shutil.copy2(zip_path, temp_zip)
                
                # 删除现有的META-INF目录
                self.remove_existing_signature(temp_zip)
                
                # 创建新的签名文件
                meta_inf_dir = temp_path / "META-INF"
                meta_inf_dir.mkdir(exist_ok=True)
                
                # 1. 创建简化的MANIFEST.MF
                manifest_content = self.create_simple_manifest(temp_zip)
                manifest_path = meta_inf_dir / "MANIFEST.MF"
                with open(manifest_path, 'w', encoding='utf-8') as f:
                    f.write(manifest_content)
                
                # 2. 创建简化的CERT.SF
                cert_sf_content = self.create_simple_cert_sf(manifest_content)
                cert_sf_path = meta_inf_dir / "CERT.SF"
                with open(cert_sf_path, 'w', encoding='utf-8') as f:
                    f.write(cert_sf_content)
                
                # 3. 使用原始证书格式
                self.copy_original_certificate(meta_inf_dir)
                
                # 4. 重新打包
                with zipfile.ZipFile(temp_zip, 'a') as zip_ref:
                    zip_ref.write(manifest_path, "META-INF/MANIFEST.MF")
                    zip_ref.write(cert_sf_path, "META-INF/CERT.SF")
                    
                    cert_files = list(meta_inf_dir.glob("*.pem"))
                    if cert_files:
                        zip_ref.write(cert_files[0], "META-INF/CERT.RSA")
                
                # 替换原文件
                shutil.copy2(temp_zip, zip_path)
                
            self.log("✅ 传统格式签名创建完成")
            return True
            
        except Exception as e:
            self.log(f"❌ 创建传统签名失败: {str(e)}")
            return False
    
    def remove_existing_signature(self, zip_path: Path):
        """移除现有签名"""
        self.log("🗑️ 移除现有签名文件...")
        
        try:
            # 读取所有非签名文件
            files_to_keep = []
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                for file_info in zip_ref.filelist:
                    if not file_info.filename.startswith("META-INF/"):
                        files_to_keep.append((file_info.filename, zip_ref.read(file_info.filename)))
            
            # 重新创建ZIP文件
            with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zip_ref:
                for filename, data in files_to_keep:
                    zip_ref.writestr(filename, data)
                    
        except Exception as e:
            self.log(f"❌ 移除签名失败: {str(e)}")
    
    def create_simple_manifest(self, zip_path: Path) -> str:
        """创建简化的MANIFEST.MF"""
        self.log("📋 生成简化文件清单...")
        
        manifest_lines = [
            "Manifest-Version: 1.0",
            "Created-By: Error21 Fix Tool - By.举个🌰",
            ""
        ]
        
        try:
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                for file_info in zip_ref.filelist:
                    if file_info.filename.startswith("META-INF/") or file_info.filename.endswith("/"):
                        continue
                    
                    file_data = zip_ref.read(file_info.filename)
                    
                    # 只使用SHA1哈希 (传统方式)
                    sha1_hash = hashlib.sha1(file_data).digest()
                    sha1_b64 = binascii.b2a_base64(sha1_hash).decode().strip()
                    
                    manifest_lines.extend([
                        f"Name: {file_info.filename}",
                        f"SHA1-Digest: {sha1_b64}",
                        ""
                    ])
        
        except Exception as e:
            self.log(f"❌ 生成清单失败: {str(e)}")
        
        return "\n".join(manifest_lines)
    
    def create_simple_cert_sf(self, manifest_content: str) -> str:
        """创建简化的CERT.SF"""
        self.log("🔐 生成简化签名文件...")
        
        # 计算清单的SHA1哈希
        manifest_bytes = manifest_content.encode('utf-8')
        manifest_sha1 = hashlib.sha1(manifest_bytes).digest()
        manifest_sha1_b64 = binascii.b2a_base64(manifest_sha1).decode().strip()
        
        cert_sf_lines = [
            "Signature-Version: 1.0",
            "Created-By: Error21 Fix Tool - By.举个🌰",
            f"SHA1-Digest-Manifest: {manifest_sha1_b64}",
            ""
        ]
        
        return "\n".join(cert_sf_lines)
    
    def copy_original_certificate(self, meta_inf_dir: Path):
        """复制原始证书"""
        self.log("📜 复制原始证书...")
        
        otacerts_path = self.system_dir / "system" / "etc" / "security" / "otacerts.zip"
        if otacerts_path.exists():
            try:
                with zipfile.ZipFile(otacerts_path, 'r') as zip_ref:
                    for file_name in zip_ref.namelist():
                        if file_name.endswith('.pem'):
                            cert_data = zip_ref.read(file_name)
                            cert_path = meta_inf_dir / file_name
                            with open(cert_path, 'wb') as f:
                                f.write(cert_data)
                            break
            except Exception as e:
                self.log(f"❌ 复制证书失败: {str(e)}")
    
    def create_unsigned_package(self, input_file: str, output_file: str = None) -> bool:
        """创建无签名包 (绕过验证)"""
        self.log("🚫 创建无签名包...")
        
        input_path = Path(input_file)
        if not input_path.exists():
            self.log(f"❌ 输入文件不存在: {input_file}")
            return False
        
        if output_file is None:
            output_file = f"unsigned_{input_path.name}"
        
        output_path = Path(output_file)
        
        try:
            # 复制文件并移除签名
            shutil.copy2(input_path, output_path)
            self.remove_existing_signature(output_path)
            
            self.log(f"✅ 无签名包创建完成: {output_path}")
            return True
            
        except Exception as e:
            self.log(f"❌ 创建无签名包失败: {str(e)}")
            return False
    
    def fix_error21_package(self, input_file: str) -> bool:
        """修复Error 21问题"""
        input_path = Path(input_file)
        if not input_path.exists():
            self.log(f"❌ 文件不存在: {input_file}")
            return False
        
        self.log(f"🔧 开始修复Error 21: {input_path.name}")
        
        # 创建多个修复版本
        fixes = [
            ("legacy_signed_" + input_path.name, self.create_legacy_signature),
            ("unsigned_" + input_path.name, lambda p: self.create_unsigned_package(str(input_path), str(p)))
        ]
        
        success_count = 0
        
        for fix_name, fix_func in fixes:
            fix_path = Path(fix_name)
            self.log(f"🔄 尝试修复方案: {fix_name}")
            
            try:
                if fix_name.startswith("legacy_"):
                    # 复制文件然后应用传统签名
                    shutil.copy2(input_path, fix_path)
                    if fix_func(fix_path):
                        success_count += 1
                        self.log(f"✅ 修复方案成功: {fix_name}")
                    else:
                        self.log(f"❌ 修复方案失败: {fix_name}")
                else:
                    # 直接创建修复版本
                    if fix_func(fix_path):
                        success_count += 1
                        self.log(f"✅ 修复方案成功: {fix_name}")
                    else:
                        self.log(f"❌ 修复方案失败: {fix_name}")
                        
            except Exception as e:
                self.log(f"❌ 修复方案异常 {fix_name}: {str(e)}")
        
        if success_count > 0:
            self.log(f"🎉 成功创建 {success_count} 个修复版本")
            self.print_usage_instructions()
            return True
        else:
            self.log("❌ 所有修复方案都失败了")
            self.print_alternative_solutions()
            return False
    
    def print_usage_instructions(self):
        """打印使用说明"""
        print("\n" + "="*60)
        print("修复版本使用说明")
        print("="*60)
        
        print("\n📦 生成的修复版本:")
        for file_path in Path(".").glob("*signed_update_diy.zip"):
            if file_path.exists():
                print(f"  ✅ {file_path.name}")
        
        for file_path in Path(".").glob("unsigned_*.zip"):
            if file_path.exists():
                print(f"  ✅ {file_path.name}")
        
        print("\n🚀 使用建议:")
        print("1. 优先尝试 legacy_signed_*.zip (传统签名格式)")
        print("2. 如果仍然Error 21，尝试 unsigned_*.zip (无签名)")
        print("3. 无签名版本需要先替换设备公钥或禁用验证")
        
        print("\n⚠️ 注意事项:")
        print("- 在测试设备上先验证")
        print("- 备份原始固件")
        print("- 确保设备型号匹配")
    
    def print_alternative_solutions(self):
        """打印替代解决方案"""
        print("\n" + "="*60)
        print("替代解决方案")
        print("="*60)
        
        print("\n🔧 手动修复方法:")
        print("1. 替换设备公钥:")
        print("   - 通过ADB root权限")
        print("   - 替换 /system/etc/security/pub_keys.txt")
        print("   - 使用自己生成的公钥")
        
        print("\n2. 禁用签名验证:")
        print("   - 修改Recovery程序")
        print("   - 跳过签名验证步骤")
        print("   - 需要Recovery源码")
        
        print("\n3. 使用原厂工具:")
        print("   - 寻找官方刷机工具")
        print("   - 使用厂商提供的签名工具")
        print("   - 联系设备制造商")

def main():
    """主函数"""
    print("🔧 Error 21 修复工具")
    print("Copyright: By.举个🌰")
    print("-" * 50)
    
    # 检查签名文件
    signed_files = list(Path(".").glob("*signed_update_diy.zip"))
    
    if not signed_files:
        print("❌ 找不到签名的卡刷包文件")
        print("请确保已经运行过官方签名工具")
        return
    
    # 创建修复工具实例
    fixer = Error21FixTool()
    
    # 分析错误
    fixer.analyze_error21()
    
    # 修复每个签名文件
    for signed_file in signed_files:
        print(f"\n🔄 处理文件: {signed_file}")
        fixer.fix_error21_package(str(signed_file))

if __name__ == "__main__":
    main()
