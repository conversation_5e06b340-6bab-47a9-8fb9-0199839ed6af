# OTA签名工具使用说明

**By.举个🌰**

## 🎯 工具简介

OTA签名工具是一个专门为晶晨机顶盒卡刷包设计的图形化签名工具，支持：

- 📦 **卡刷包分析** - 分析ZIP文件结构和内容
- 🔑 **密钥生成** - 生成RSA签名密钥对和证书
- ✍️ **包签名** - 对卡刷包进行数字签名
- 🖥️ **图形界面** - 友好的GUI操作界面

## 🚀 快速启动

### 方法1: 使用专用启动器 (推荐)
```cmd
start_ota_tool.bat
```

### 方法2: 使用综合启动器
```powershell
.\run_analysis.ps1
# 选择选项 4
```

### 方法3: 直接运行
```cmd
# 激活虚拟环境
firmware_analysis_env\Scripts\activate.bat

# 运行工具
python ota_signature_tool.py
```

## 📋 使用步骤

### 第一步：选择文件
1. 点击"浏览"按钮选择要签名的卡刷包(.zip文件)
2. 选择输出目录(签名后的文件保存位置)

### 第二步：分析卡刷包 (可选)
1. 点击"🔍 分析卡刷包"按钮
2. 工具会显示：
   - ZIP文件结构
   - 关键文件列表
   - 现有签名信息
   - 文件哈希值

### 第三步：生成签名密钥
1. 选择签名算法(RSA-2048或RSA-4096)
2. 点击"🔑 生成密钥"按钮
3. 工具会生成：
   - `ota_private_key.pem` - 私钥文件
   - `ota_certificate.pem` - 证书文件
   - `ota_public_key.txt` - 公钥(十六进制)

### 第四步：签名卡刷包
1. 确保已生成签名密钥
2. 点击"✍️ 签名卡刷包"按钮
3. 工具会生成：
   - `signed_原文件名.zip` - 签名后的卡刷包
   - `signature_info_*.txt` - 签名信息文件

## 🔧 界面功能说明

### 📁 文件选择区域
- **卡刷包文件**: 选择要签名的ZIP文件
- **输出目录**: 选择签名文件的保存位置

### ⚙️ 签名配置区域
- **签名算法**: 选择RSA-2048或RSA-4096
- **密钥选项**: 
  - 使用现有密钥: 使用已有的密钥文件
  - 生成新密钥: 创建新的签名密钥

### 🎛️ 操作按钮
- **🔍 分析卡刷包**: 分析ZIP文件结构和内容
- **🔑 生成密钥**: 生成RSA签名密钥对
- **✍️ 签名卡刷包**: 对卡刷包进行数字签名
- **🧹 清除日志**: 清空操作日志

### 📊 进度显示
- 进度条显示当前操作进度
- 状态文本显示具体操作步骤

### 📋 操作日志
- 实时显示操作过程和结果
- 包含时间戳的详细日志信息

## 📄 输出文件说明

### 密钥文件
```
ota_private_key.pem     # RSA私钥(PEM格式)
ota_certificate.pem     # X.509证书(PEM格式)  
ota_public_key.txt      # 公钥十六进制字符串
```

### 签名文件
```
signed_原文件名.zip     # 签名后的卡刷包
signature_info_*.txt    # 签名信息和元数据
```

### 签名包结构
签名后的ZIP文件会包含：
```
META-INF/
├── MANIFEST.MF         # 文件清单和哈希
├── CERT.SF            # 签名文件
└── CERT.RSA           # RSA证书
```

## 🔐 签名原理

### 签名过程
1. **生成密钥对**: 创建RSA私钥和对应的公钥证书
2. **计算文件哈希**: 对ZIP中每个文件计算SHA-256哈希
3. **创建清单**: 生成包含所有文件哈希的MANIFEST.MF
4. **签名清单**: 使用私钥对清单进行数字签名
5. **打包签名**: 将签名信息添加到ZIP的META-INF目录

### 验证过程
1. **提取证书**: 从META-INF/CERT.RSA提取公钥证书
2. **验证签名**: 使用公钥验证CERT.SF的签名
3. **校验哈希**: 重新计算文件哈希并与清单对比
4. **完整性检查**: 确认所有文件未被篡改

## 🎯 应用场景

### 固件定制
- 修改系统应用后重新签名
- 添加自定义功能到固件包
- 绕过原厂签名验证

### 安全研究
- 分析OTA包的签名机制
- 测试签名验证的安全性
- 研究固件更新流程

### 开发测试
- 为测试固件添加签名
- 验证签名算法的正确性
- 调试OTA更新过程

## ⚠️ 注意事项

### 密钥安全
- 🔒 私钥文件需要妥善保管
- 🚫 不要将私钥上传到公共仓库
- 🔄 定期更换签名密钥

### 兼容性
- ✅ 支持标准ZIP格式的卡刷包
- ✅ 兼容Android OTA签名规范
- ⚠️ 某些设备可能需要特定的签名格式

### 法律合规
- 📖 仅用于学习和研究目的
- 🚫 不得用于破解商业固件
- ⚖️ 遵守相关法律法规

## 🐛 故障排除

### 常见问题

**Q: 提示"找不到签名密钥文件"**
A: 请先点击"生成密钥"按钮创建签名密钥

**Q: 签名后的文件无法使用**
A: 检查目标设备是否支持自定义签名，可能需要替换设备上的验证公钥

**Q: 工具启动失败**
A: 确保Python环境正确，运行`start_ota_tool.bat`自动配置环境

**Q: 签名过程中断**
A: 检查磁盘空间是否充足，确保ZIP文件没有损坏

### 日志分析
- ✅ 绿色消息表示操作成功
- ❌ 红色消息表示操作失败
- 📝 蓝色消息表示信息提示
- ⚠️ 黄色消息表示警告信息

## 📞 技术支持

如遇到问题，请检查：
1. Python环境是否正确安装
2. 虚拟环境是否正常激活
3. 依赖包是否完整安装
4. 文件权限是否充足

## 📄 版权信息

**Copyright © 2025 By.举个🌰**

本工具仅供学习和研究使用，请遵守相关法律法规。

---

**祝你使用愉快！🎉**
