[global]
ntp_server
ntp_server2
wifiap_on
bluetooth_on
wifi_on
[secure]
install_non_market_apps
tr069_blacklist
tr069_usb_whitelist
tr069_usb_whitelist_enable
tr069_blacklist_enable
has_ui
forceUpgrade
ethernet_on
ethernet_mode
ethernet_ifname
ethernet_ip
ethernet_prefixlength
ethernet_dns1
ethernet_dns2
ethernet_iproute
option60_on
option125_on
pppoe_on
pppoe_via_device
pppoe_user_name
pppoe_user_pass
bluetooth_name
[properties]
persist.sys.dns.protect.enable
persist.sys.itms.epg.launcher
[file]
/data/misc/wifi/wpa_supplicant.conf system wifi 0644
/data/misc/wifi/softap.conf system system 0600
/data/ppp/pppd.pid root root 0666
/data/ppp/pppoe.pid root root 0666
/data/ppp/pppoe.session root root 0666
/data/misc/ethernet/ipconfig.txt system system 0600
/data/misc/ethernet/eth0_config_prop.txt system system 0600
