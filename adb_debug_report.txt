================================================================================
ADB调试分析报告
By.举个🌰
================================================================================

🔗 设备地址: 192.168.233.180:5555

🔐 安全相关属性:
  dalvik.vm.dex2oat-minidebuginfo: true
  debug.atrace.tags.enableflags: 0
  debug.force_rtl: 0
  debug.hwui.use_buffer_age: false
  debug.sf.disable_backpressure: 1
  debug.sf.latch_unsignaled: 1
  init.svc.adbd: running
  init.svc.keystore: running
  init.svc.swRootService: running
  init.svc.vendor.keymaster-3-0: running
  libplayer.cache.debug: 1
  media.amplayer.seekkeyframe: 1
  persist.audio.debug.read: 
  persist.audio.debug.search: 
  persist.sys.cec.enable.key: true
  persist.sys.cec.enable.volume.key: false
  ro.andlink.authKey: 
  ro.andlink.authkey: 00010001EE7717ADC41AFF48DE57EADE521FD7B93715A2474437E6C266330D1D1C768FD9
  ro.boottime.adbd: 44794115561
  ro.boottime.keystore: 4773032376
  ro.boottime.swRootService: 4732795501
  ro.boottime.vendor.keymaster-3-0: **********
  ro.build.system_root_image: false
  ro.debuggable: 1
  ro.secure: 1
  service.adb.root: 1
  service.adb.tcp.port: 5555
  sys.debug.system.prop: /system/build_CM311-1e-ZG.prop
  sys.debug.vendor.prop: /vendor/build_CM311-1e-ZG.prop
  sys.key.direcet.report: true
  sys.sw.adb.enabled: 1

🔍 签名验证信息:
  selinux_status: Permissive
  security_patch: 2018-08-05

🔄 运行进程数量: 0
  关键进程:

📦 已安装应用数量: 64
  系统关键应用:
    com.android.providers.media
    com.android.companiondevicemanager
    com.android.defcontainer
    com.android.certinstaller
    android
    com.android.statementservice
    com.android.providers.settings
    com.android.silenceinstaller
    com.android.webview
    android.ext.shared

================================================================================
调试分析完成
================================================================================