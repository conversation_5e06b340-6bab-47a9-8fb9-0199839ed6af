# Copyright (C) 2017 The Android Open Source Project
#
# Bionic loader config file.
#

# Don't change the order here. The first pattern that matches with the
# absolute path of an executable is selected.
dir.system = /system/bin/
dir.system = /system/xbin/
dir.system = /product/bin/
dir.system = /ctc/bin/

dir.vendor = /odm/bin/
dir.vendor = /vendor/bin/
dir.vendor = /data/nativetest/odm
dir.vendor = /data/nativetest64/odm
dir.vendor = /data/benchmarktest/odm
dir.vendor = /data/benchmarktest64/odm
dir.vendor = /data/nativetest/vendor
dir.vendor = /data/nativetest64/vendor
dir.vendor = /data/benchmarktest/vendor
dir.vendor = /data/benchmarktest64/vendor

dir.system = /data/nativetest
dir.system = /data/nativetest64
dir.system = /data/benchmarktest
dir.system = /data/benchmarktest64

dir.postinstall = /postinstall

[system]
additional.namespaces = sphal,vndk,rs

###############################################################################
# "default" namespace
#
# Framework-side code runs in this namespace. Libs from /vendor partition
# can't be loaded in this namespace.
###############################################################################
namespace.default.isolated = true

namespace.default.search.paths  = /system/${LIB}
namespace.default.search.paths += /product/${LIB}
namespace.default.search.paths += /ctc/${LIB}

# We can't have entire /system/${LIB} as permitted paths because doing so
# makes it possible to load libs in /system/${LIB}/vndk* directories by
# their absolute paths (e.g. dlopen("/system/lib/vndk/libbase.so");).
# VNDK libs are built with previous versions of Android and thus must not be
# loaded into this namespace where libs built with the current version of
# Android are loaded. Mixing the two types of libs in the same namespace can
# cause unexpected problem.
namespace.default.permitted.paths  = /system/${LIB}/drm
namespace.default.permitted.paths += /system/${LIB}/extractors
namespace.default.permitted.paths += /system/${LIB}/amplayer
namespace.default.permitted.paths += /system/${LIB}/hw
namespace.default.permitted.paths += /product/${LIB}
namespace.default.permitted.paths += /ctc/${LIB}
# These are where odex files are located. libart has to be able to dlopen the files
namespace.default.permitted.paths += /system/framework
namespace.default.permitted.paths += /system/app
namespace.default.permitted.paths += /system/priv-app
namespace.default.permitted.paths += /vendor/framework
namespace.default.permitted.paths += /vendor/app
namespace.default.permitted.paths += /vendor/priv-app
namespace.default.permitted.paths += /odm/framework
namespace.default.permitted.paths += /odm/app
namespace.default.permitted.paths += /odm/priv-app
namespace.default.permitted.paths += /oem/app
namespace.default.permitted.paths += /product/framework
namespace.default.permitted.paths += /product/app
namespace.default.permitted.paths += /product/priv-app
namespace.default.permitted.paths += /ctc/app
namespace.default.permitted.paths += /ctc/priv-app
namespace.default.permitted.paths += /data
namespace.default.permitted.paths += /mnt/expand

namespace.default.asan.search.paths  = /data/asan/system/${LIB}
namespace.default.asan.search.paths +=           /system/${LIB}
namespace.default.asan.search.paths += /data/asan/product/${LIB}
namespace.default.asan.search.paths +=           /product/${LIB}
namespace.default.asan.search.paths += /data/asan/ctc/${LIB}
namespace.default.asan.search.paths +=           /ctc/${LIB}

namespace.default.asan.permitted.paths  = /data
namespace.default.asan.permitted.paths += /system/${LIB}/drm
namespace.default.asan.permitted.paths += /system/${LIB}/extractors
namespace.default.asan.permitted.paths += /system/${LIB}/hw
namespace.default.asan.permitted.paths += /system/framework
namespace.default.asan.permitted.paths += /system/app
namespace.default.asan.permitted.paths += /system/priv-app
namespace.default.asan.permitted.paths += /vendor/framework
namespace.default.asan.permitted.paths += /vendor/app
namespace.default.asan.permitted.paths += /vendor/priv-app
namespace.default.asan.permitted.paths += /odm/framework
namespace.default.asan.permitted.paths += /odm/app
namespace.default.asan.permitted.paths += /odm/priv-app
namespace.default.asan.permitted.paths += /oem/app
namespace.default.asan.permitted.paths += /product/${LIB}
namespace.default.asan.permitted.paths += /product/framework
namespace.default.asan.permitted.paths += /product/app
namespace.default.asan.permitted.paths += /product/priv-app
namespace.default.asan.permitted.paths += /ctc/${LIB}
namespace.default.asan.permitted.paths += /ctc/app
namespace.default.asan.permitted.paths += /ctc/priv-app
namespace.default.asan.permitted.paths += /mnt/expand

###############################################################################
# "sphal" namespace
#
# SP-HAL(Sameprocess-HAL)s are the only vendor libraries that are allowed to be
# loaded inside system processes. libEGL_<chipset>.so, libGLESv2_<chipset>.so,
# <EMAIL>, etc are SP-HALs.
#
# This namespace is exclusivly for SP-HALs. When the framework tries to dynami-
# cally load SP-HALs, android_dlopen_ext() is used to explicitly specifying
# that they should be searched and loaded from this namespace.
#
# Note that there is no link from the default namespace to this namespace.
###############################################################################
namespace.sphal.isolated = true
namespace.sphal.visible = true

namespace.sphal.search.paths  = /odm/${LIB}
namespace.sphal.search.paths += /vendor/${LIB}

namespace.sphal.permitted.paths  = /odm/${LIB}
namespace.sphal.permitted.paths += /vendor/${LIB}

namespace.sphal.asan.search.paths  = /data/asan/odm/${LIB}
namespace.sphal.asan.search.paths +=           /odm/${LIB}
namespace.sphal.asan.search.paths += /data/asan/vendor/${LIB}
namespace.sphal.asan.search.paths +=           /vendor/${LIB}

namespace.sphal.asan.permitted.paths  = /data/asan/odm/${LIB}
namespace.sphal.asan.permitted.paths +=           /odm/${LIB}
namespace.sphal.asan.permitted.paths += /data/asan/vendor/${LIB}
namespace.sphal.asan.permitted.paths +=           /vendor/${LIB}

# Once in this namespace, access to libraries in /system/lib is restricted. Only
# libs listed here can be used.
namespace.sphal.links = default,vndk,rs

namespace.sphal.link.default.shared_libs  = libEGL.so:libGLESv1_CM.so:libGLESv2.so:libGLESv3.so:libRS.so:libandroid_net.so:libc.so:libclang_rt.asan-arm-android.so:libdl.so:liblog.so:libm.so:libmediandk.so:libnativewindow.so:libneuralnetworks.so:libsync.so:libvndksupport.so:libvulkan.so
namespace.sphal.link.default.shared_libs += libclang_rt.asan-arm-android.so:libclang_rt.ubsan_standalone-arm-android.so:libclang_rt.tsan-arm-android.so

namespace.sphal.link.vndk.shared_libs = <EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:libRSCpuRef.so:libRSDriver.so:libRS_internal.so:libbase.so:libbcinfo.so:libc++.so:libcutils.so:libhardware.so:libhidlbase.so:libhidlmemory.so:libhidltransport.so:libhwbinder.so:libhwbinder_noltopgo.so:libion.so:liblzma.so:libunwindstack.so:libutils.so:libutilscallstack.so:libz.so

# Renderscript gets separate namespace
namespace.sphal.link.rs.shared_libs = libRS_internal.so

###############################################################################
# "rs" namespace
#
# This namespace is exclusively for Renderscript internal libraries.
# This namespace has slightly looser restriction than the vndk namespace because
# of the genuine characteristics of Renderscript; /data is in the permitted path
# to load the compiled *.so file and libmediandk.so can be used here.
###############################################################################
namespace.rs.isolated = true
namespace.rs.visible = true

namespace.rs.search.paths  = /odm/${LIB}/vndk-sp
namespace.rs.search.paths += /vendor/${LIB}/vndk-sp
namespace.rs.search.paths += /system/${LIB}/vndk-sp-28
namespace.rs.search.paths += /odm/${LIB}
namespace.rs.search.paths += /vendor/${LIB}

namespace.rs.permitted.paths  = /odm/${LIB}
namespace.rs.permitted.paths += /vendor/${LIB}
namespace.rs.permitted.paths += /data

namespace.rs.asan.search.paths  = /data/asan/odm/${LIB}/vndk-sp
namespace.rs.asan.search.paths +=           /odm/${LIB}/vndk-sp
namespace.rs.asan.search.paths += /data/asan/vendor/${LIB}/vndk-sp
namespace.rs.asan.search.paths +=           /vendor/${LIB}/vndk-sp
namespace.rs.asan.search.paths += /data/asan/system/${LIB}/vndk-sp-28
namespace.rs.asan.search.paths +=           /system/${LIB}/vndk-sp-28
namespace.rs.asan.search.paths += /data/asan/odm/${LIB}
namespace.rs.asan.search.paths +=           /odm/${LIB}
namespace.rs.asan.search.paths += /data/asan/vendor/${LIB}
namespace.rs.asan.search.paths +=           /vendor/${LIB}

namespace.rs.asan.permitted.paths  = /data/asan/odm/${LIB}
namespace.rs.asan.permitted.paths +=           /odm/${LIB}
namespace.rs.asan.permitted.paths += /data/asan/vendor/${LIB}
namespace.rs.asan.permitted.paths +=           /vendor/${LIB}
namespace.rs.asan.permitted.paths += /data

namespace.rs.links = default,vndk

namespace.rs.link.default.shared_libs  =  libEGL.so:libGLESv1_CM.so:libGLESv2.so:libGLESv3.so:libRS.so:libandroid_net.so:libc.so:libclang_rt.asan-arm-android.so:libdl.so:liblog.so:libm.so:libmediandk.so:libnativewindow.so:libneuralnetworks.so:libsync.so:libvndksupport.so:libvulkan.so
namespace.rs.link.default.shared_libs += libclang_rt.asan-arm-android.so:libclang_rt.ubsan_standalone-arm-android.so:libclang_rt.tsan-arm-android.so
# Private LLNDK libs (e.g. libft2.so) are exceptionally allowed to this
# namespace because RS framework libs are using them.
namespace.rs.link.default.shared_libs += libft2.so

namespace.rs.link.vndk.shared_libs = <EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:libRSCpuRef.so:libRSDriver.so:libRS_internal.so:libbase.so:libbcinfo.so:libc++.so:libcutils.so:libhardware.so:libhidlbase.so:libhidlmemory.so:libhidltransport.so:libhwbinder.so:libhwbinder_noltopgo.so:libion.so:liblzma.so:libunwindstack.so:libutils.so:libutilscallstack.so:libz.so

###############################################################################
# "vndk" namespace
#
# This namespace is exclusively for vndk-sp libs.
###############################################################################
namespace.vndk.isolated = true
namespace.vndk.visible = true

namespace.vndk.search.paths  = /odm/${LIB}/vndk-sp
namespace.vndk.search.paths += /vendor/${LIB}/vndk-sp
namespace.vndk.search.paths += /system/${LIB}/vndk-sp-28

namespace.vndk.permitted.paths  = /odm/${LIB}/hw
namespace.vndk.permitted.paths += /odm/${LIB}/egl
namespace.vndk.permitted.paths += /vendor/${LIB}/hw
namespace.vndk.permitted.paths += /vendor/${LIB}/egl
# This is exceptionally <NAME_EMAIL> is here
namespace.vndk.permitted.paths += /system/${LIB}/vndk-sp-28/hw

namespace.vndk.asan.search.paths  = /data/asan/odm/${LIB}/vndk-sp
namespace.vndk.asan.search.paths +=           /odm/${LIB}/vndk-sp
namespace.vndk.asan.search.paths += /data/asan/vendor/${LIB}/vndk-sp
namespace.vndk.asan.search.paths +=           /vendor/${LIB}/vndk-sp
namespace.vndk.asan.search.paths += /data/asan/system/${LIB}/vndk-sp-28
namespace.vndk.asan.search.paths +=           /system/${LIB}/vndk-sp-28

namespace.vndk.asan.permitted.paths  = /data/asan/odm/${LIB}/hw
namespace.vndk.asan.permitted.paths +=           /odm/${LIB}/hw
namespace.vndk.asan.permitted.paths += /data/asan/odm/${LIB}/egl
namespace.vndk.asan.permitted.paths +=           /odm/${LIB}/egl
namespace.vndk.asan.permitted.paths += /data/asan/vendor/${LIB}/hw
namespace.vndk.asan.permitted.paths +=           /vendor/${LIB}/hw
namespace.vndk.asan.permitted.paths += /data/asan/vendor/${LIB}/egl
namespace.vndk.asan.permitted.paths +=           /vendor/${LIB}/egl

namespace.vndk.asan.permitted.paths += /data/asan/system/${LIB}/vndk-sp-28/hw
namespace.vndk.asan.permitted.paths +=           /system/${LIB}/vndk-sp-28/hw

# The "vndk" namespace links to "default" namespace for LLNDK libs and links to
# "sphal" namespace for vendor libs.  The ordering matters.  The "default"
# namespace has higher priority than the "sphal" namespace.
namespace.vndk.links = default,sphal

# When these NDK libs are required inside this namespace, then it is redirected
# to the default namespace. This is possible since their ABI is stable across
# Android releases.
namespace.vndk.link.default.shared_libs  = libEGL.so:libGLESv1_CM.so:libGLESv2.so:libGLESv3.so:libRS.so:libandroid_net.so:libc.so:libclang_rt.asan-arm-android.so:libdl.so:liblog.so:libm.so:libmediandk.so:libnativewindow.so:libneuralnetworks.so:libsync.so:libvndksupport.so:libvulkan.so
namespace.vndk.link.default.shared_libs += libclang_rt.asan-arm-android.so:libclang_rt.ubsan_standalone-arm-android.so:libclang_rt.tsan-arm-android.so

# Allow VNDK-SP extensions to use vendor libraries
namespace.vndk.link.sphal.allow_all_shared_libs = true

###############################################################################
# Namespace config for vendor processes. In O, no restriction is enforced for
# them. However, in O-MR1, access to /system/${LIB} will not be allowed to
# the default namespace. 'system' namespace will be added to give limited
# (LL-NDK only) access.
###############################################################################
[vendor]
additional.namespaces = system,vndk

###############################################################################
# "default" namespace
#
# This is the default linker namespace for a vendor process (a process started
# from /vendor/bin/*). The main executable and the libs under /vendor/lib[64]
# are loaded directly into this namespace. However, other libs under the system
# partition (VNDK and LLNDK libraries) are not loaded here but from the
# separate namespace 'system'. The delegation to the system namespace is done
# via the 'namespace.default.link.system.shared_libs' property below.
###############################################################################
namespace.default.isolated = true
namespace.default.visible = true

namespace.default.search.paths  = /odm/${LIB}
namespace.default.search.paths += /vendor/${LIB}

namespace.default.permitted.paths  = /odm
namespace.default.permitted.paths += /vendor

namespace.default.asan.search.paths  = /data/asan/odm/${LIB}
namespace.default.asan.search.paths +=           /odm/${LIB}
namespace.default.asan.search.paths += /data/asan/vendor/${LIB}
namespace.default.asan.search.paths +=           /vendor/${LIB}

namespace.default.asan.permitted.paths  = /data/asan/odm
namespace.default.asan.permitted.paths +=           /odm
namespace.default.asan.permitted.paths += /data/asan/vendor
namespace.default.asan.permitted.paths +=           /vendor

namespace.default.links = system,vndk
namespace.default.link.system.shared_libs = libEGL.so:libGLESv1_CM.so:libGLESv2.so:libGLESv3.so:libRS.so:libandroid_net.so:libc.so:libclang_rt.asan-arm-android.so:libdl.so:liblog.so:libm.so:libmediandk.so:libnativewindow.so:libneuralnetworks.so:libsync.so:libvndksupport.so:libvulkan.so
namespace.default.link.vndk.shared_libs  = <EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:libRSCpuRef.so:libRSDriver.so:libRS_internal.so:libbase.so:libbcinfo.so:libc++.so:libcutils.so:libhardware.so:libhidlbase.so:libhidlmemory.so:libhidltransport.so:libhwbinder.so:libhwbinder_noltopgo.so:libion.so:liblzma.so:libunwindstack.so:libutils.so:libutilscallstack.so:libz.so
namespace.default.link.vndk.shared_libs += <EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:android.hardware.audio.common-util.so:<EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:android.hardware.configstore-utils.so:<EMAIL>:<EMAIL>:android.hardware.confirmationui-support-lib.so:<EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:<EMAIL>:libadf.so:libaudioroute.so:libaudioutils.so:libbinder.so:libcamera_metadata.so:libcap.so:libclang_rt.ubsan_standalone-arm-android.so:libcn-cbor.so:libcrypto.so:libcrypto_utils.so:libcurl.so:libdiskconfig.so:libdumpstateutil.so:libevent.so:libexif.so:libexpat.so:libfmq.so:libgatekeeper.so:libhardware_legacy.so:libhidlallocatorutils.so:libhidlcache.so:libjpeg.so:libkeymaster_messages.so:libkeymaster_portable.so:libldacBT_abr.so:libldacBT_enc.so:liblz4.so:libmedia_helper.so:libmedia_omx.so:libmemtrack.so:libminijail.so:libmkbootimg_abi_check.so:libnetutils.so:libnl.so:libopus.so:libpagemap.so:libpcre2.so:libpiex.so:libpng.so:libpower.so:libprocinfo.so:libprotobuf-cpp-full.so:libprotobuf-cpp-lite.so:libpuresoftkeymasterdevice.so:libradio_metadata.so:libselinux.so:libsoftkeymasterdevice.so:libspeexresampler.so:libsqlite.so:libssl.so:libstagefright_amrnb_common.so:libstagefright_bufferqueue_helper.so:libstagefright_enc_common.so:libstagefright_flacdec.so:libstagefright_foundation.so:libstagefright_omx.so:libstagefright_omx_utils.so:libstagefright_soft_aacdec.so:libstagefright_soft_aacenc.so:libstagefright_soft_amrdec.so:libstagefright_soft_amrnbenc.so:libstagefright_soft_amrwbenc.so:libstagefright_soft_avcdec.so:libstagefright_soft_avcenc.so:libstagefright_soft_flacdec.so:libstagefright_soft_flacenc.so:libstagefright_soft_g711dec.so:libstagefright_soft_gsmdec.so:libstagefright_soft_hevcdec.so:libstagefright_soft_mp3dec.so:libstagefright_soft_mpeg2dec.so:libstagefright_soft_mpeg4dec.so:libstagefright_soft_mpeg4enc.so:libstagefright_soft_opusdec.so:libstagefright_soft_rawdec.so:libstagefright_soft_vorbisdec.so:libstagefright_soft_vpxdec.so:libstagefright_soft_vpxenc.so:libstagefright_xmlparser.so:libsuspend.so:libsysutils.so:libtinyalsa.so:libtinyxml2.so:libui.so:libusbhost.so:libvixl-arm.so:libvixl-arm64.so:libvorbisidec.so:libwifi-system-iface.so:libxml2.so:libyuv.so:libziparchive.so

###############################################################################
# "vndk" namespace
#
# This namespace is where VNDK and VNDK-SP libraries are loaded for
# a vendor process.
###############################################################################
namespace.vndk.isolated = false

namespace.vndk.search.paths  = /odm/${LIB}/vndk
namespace.vndk.search.paths += /odm/${LIB}/vndk-sp
namespace.vndk.search.paths += /vendor/${LIB}/vndk
namespace.vndk.search.paths += /vendor/${LIB}/vndk-sp
namespace.vndk.search.paths += /system/${LIB}/vndk-sp-28
namespace.vndk.search.paths += /system/${LIB}/vndk-28

namespace.vndk.asan.search.paths  = /data/asan/odm/${LIB}/vndk
namespace.vndk.asan.search.paths +=           /odm/${LIB}/vndk
namespace.vndk.asan.search.paths += /data/asan/odm/${LIB}/vndk-sp
namespace.vndk.asan.search.paths +=           /odm/${LIB}/vndk-sp
namespace.vndk.asan.search.paths += /data/asan/vendor/${LIB}/vndk
namespace.vndk.asan.search.paths +=           /vendor/${LIB}/vndk
namespace.vndk.asan.search.paths += /data/asan/vendor/${LIB}/vndk-sp
namespace.vndk.asan.search.paths +=           /vendor/${LIB}/vndk-sp
namespace.vndk.asan.search.paths += /data/asan/system/${LIB}/vndk-sp-28
namespace.vndk.asan.search.paths +=           /system/${LIB}/vndk-sp-28
namespace.vndk.asan.search.paths += /data/asan/system/${LIB}/vndk-28
namespace.vndk.asan.search.paths +=           /system/${LIB}/vndk-28

# When these NDK libs are required inside this namespace, then it is redirected
# to the system namespace. This is possible since their ABI is stable across
# Android releases.
namespace.vndk.links = system,default
namespace.vndk.link.system.shared_libs  = libEGL.so:libGLESv1_CM.so:libGLESv2.so:libGLESv3.so:libRS.so:libandroid_net.so:libc.so:libclang_rt.asan-arm-android.so:libdl.so:liblog.so:libm.so:libmediandk.so:libnativewindow.so:libneuralnetworks.so:libsync.so:libvndksupport.so:libvulkan.so
namespace.vndk.link.system.shared_libs += libclang_rt.asan-arm-android.so:libclang_rt.ubsan_standalone-arm-android.so:libclang_rt.tsan-arm-android.so

namespace.vndk.link.default.allow_all_shared_libs = true

###############################################################################
# "system" namespace
#
# This namespace is where system libs (VNDK and LLNDK libs) are loaded for
# a vendor process.
###############################################################################
namespace.system.isolated = false

namespace.system.search.paths  = /system/${LIB}
namespace.system.search.paths += /product/${LIB}
namespace.system.search.paths += /ctc/${LIB}

namespace.system.asan.search.paths  = /data/asan/system/${LIB}
namespace.system.asan.search.paths +=           /system/${LIB}
namespace.system.asan.search.paths += /data/asan/product/${LIB}
namespace.system.asan.search.paths +=           /product/${LIB}
namespace.system.asan.search.paths += /data/asan/ctc/${LIB}
namespace.system.asan.search.paths +=           /ctc/${LIB}

###############################################################################
# Namespace config for binaries under /postinstall.
# Only one default namespace is defined and it has no directories other than
# /system/lib in the search paths. This is because linker calls realpath on the
# search paths and this causes selinux denial if the paths (/vendor, /odm) are
# not allowed to the poinstall binaries. There is no reason to allow the
# binaries to access the paths.
###############################################################################
[postinstall]
namespace.default.isolated = false
namespace.default.search.paths  = /system/${LIB}
namespace.default.search.paths += /product/${LIB}
namespace.default.search.paths += /ctc/${LIB}
