# This file is dual licensed under the terms of the Apache License, Version
# 2.0, and the BSD License. See the LICENSE file in the root of this repository
# for complete details.

from cryptography.hazmat.primitives.asymmetric import x25519
from cryptography.utils import Buffer

class X25519PrivateKey: ...
class X25519PublicKey: ...

def generate_key() -> x25519.X25519PrivateKey: ...
def from_private_bytes(data: Buffer) -> x25519.X25519PrivateKey: ...
def from_public_bytes(data: bytes) -> x25519.X25519PublicKey: ...
