#
# Copyright (c) 2004, 2013, Oracle and/or its affiliates. All rights reserved.
# ORACLE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
#

error.internal.badmsg=\u5185\u90E8\u9519\u8BEF, \u672A\u77E5\u6D88\u606F
error.badinst.nojre=\u9519\u8BEF\u5B89\u88C5\u3002\u914D\u7F6E\u6587\u4EF6\u4E2D\u627E\u4E0D\u5230 JRE
error.launch.execv=\u8C03\u7528 Java Web Start (execv) \u65F6\u9047\u5230\u9519\u8BEF
error.launch.sysexec=\u8C03\u7528 Java Web Start (SysExec) \u65F6\u9047\u5230\u9519\u8BEF
error.listener.failed=\u542F\u52A8\u5C4F\u5E55: sysCreateListenerSocket \u5931\u8D25
error.accept.failed=\u542F\u52A8\u5C4F\u5E55: \u63A5\u53D7\u5931\u8D25
error.recv.failed=\u542F\u52A8\u5C4F\u5E55: recv \u5931\u8D25
error.invalid.port=\u542F\u52A8\u5C4F\u5E55: \u672A\u6062\u590D\u6709\u6548\u7AEF\u53E3
error.read=\u8BFB\u53D6\u8D85\u51FA\u7F13\u51B2\u533A\u7ED3\u5C3E
error.xmlparsing=XML \u89E3\u6790\u9519\u8BEF: \u53D1\u73B0\u9519\u8BEF\u7684\u6807\u8BB0\u7C7B\u578B
error.splash.exit=Java Web Start \u542F\u52A8\u5C4F\u5E55\u8FDB\u7A0B\u6B63\u5728\u9000\u51FA.....\n
# "Last WinSock Error" means the error message for the last operation that failed.
error.winsock=\t\u4E0A\u4E00\u4E2A WinSock \u9519\u8BEF: 
error.winsock.load=\u65E0\u6CD5\u52A0\u8F7D winsock.dll
error.winsock.start=WSAStartup \u5931\u8D25
error.badinst.nohome=\u9519\u8BEF\u5B89\u88C5: JAVAWS_HOME \u672A\u8BBE\u7F6E 
error.splash.noimage=\u542F\u52A8\u5C4F\u5E55: \u65E0\u6CD5\u52A0\u8F7D\u542F\u52A8\u5C4F\u5E55\u56FE\u50CF
error.splash.socket=\u542F\u52A8\u5C4F\u5E55: \u670D\u52A1\u5668\u5957\u63A5\u5B57\u5931\u8D25
error.splash.cmnd=\u542F\u52A8\u5C4F\u5E55: \u65E0\u6CD5\u8BC6\u522B\u7684\u547D\u4EE4
error.splash.port=\u542F\u52A8\u5C4F\u5E55: \u672A\u6307\u5B9A\u7AEF\u53E3
error.splash.send=\u542F\u52A8\u5C4F\u5E55: \u53D1\u9001\u5931\u8D25
error.splash.timer=\u542F\u52A8\u5C4F\u5E55: \u65E0\u6CD5\u521B\u5EFA\u5173\u673A\u8BA1\u65F6\u5668
error.splash.x11.open=\u542F\u52A8\u5C4F\u5E55: \u65E0\u6CD5\u6253\u5F00 X11 \u663E\u793A
error.splash.x11.connect=\u542F\u52A8\u5C4F\u5E55: X11 \u8FDE\u63A5\u5931\u8D25
# Javaws usage: '\' is a joining of two sentence,which are connected including
# the invisible character '\n'.
message.javaws.usage=\n\u7528\u6CD5:\tjavaws [\u8FD0\u884C\u9009\u9879] <jnlp-file>\t\n\tjavaws [\u63A7\u5236\u9009\u9879]\t\t\n\n\u5176\u4E2D\u8FD0\u884C\u9009\u9879\u5305\u62EC:\t\t\t\n-verbose       \t\u663E\u793A\u5176\u4ED6\u8F93\u51FA\u5185\u5BB9\t\n-offline       \t\u4EE5\u8131\u673A\u6A21\u5F0F\u8FD0\u884C\u5E94\u7528\u7A0B\u5E8F\t\n-system        \t\u4EC5\u4ECE\u7CFB\u7EDF\u9AD8\u901F\u7F13\u5B58\u8FD0\u884C\u5E94\u7528\u7A0B\u5E8F\n-Xnosplash     \t\u8FD0\u884C\u65F6\u4E0D\u663E\u793A\u542F\u52A8\u5C4F\u5E55\t\n-J<\u9009\u9879>     \t\u4E3A vm \u63D0\u4F9B\u9009\u9879\t\n-wait          \t\u542F\u52A8 Java \u8FDB\u7A0B\u5E76\u7B49\u5F85\u5176\u9000\u51FA\t\n\n\u63A7\u5236\u9009\u9879\u5305\u62EC:\t\n-viewer        \t\u5728 Java \u63A7\u5236\u9762\u677F\u4E2D\u663E\u793A\u9AD8\u901F\u7F13\u5B58\u67E5\u770B\u5668\n-clearcache    \t\u4ECE\u9AD8\u901F\u7F13\u5B58\u5220\u9664\u6240\u6709\u672A\u5B89\u88C5\u7684\u5E94\u7528\u7A0B\u5E8F\n-uninstall     \t\u4ECE\u9AD8\u901F\u7F13\u5B58\u5220\u9664\u6240\u6709\u5E94\u7528\u7A0B\u5E8F\n-uninstall <jnlp-file>              \t\u4ECE\u9AD8\u901F\u7F13\u5B58\u5220\u9664\u5E94\u7528\u7A0B\u5E8F\t\n-import [\u5BFC\u5165\u9009\u9879] <jnlp-file>\t\u5C06\u5E94\u7528\u7A0B\u5E8F\u5BFC\u5165\u9AD8\u901F\u7F13\u5B58\t\t\n\n\u5BFC\u5165\u9009\u9879\u5305\u62EC:\t\t\t\t\t\t\n-silent        \t\u4EE5\u65E0\u63D0\u793A\u6A21\u5F0F (\u4E0D\u51FA\u73B0\u7528\u6237\u754C\u9762) \u5BFC\u5165\t\n-system        \t\u5C06\u5E94\u7528\u7A0B\u5E8F\u5BFC\u5165\u7CFB\u7EDF\u9AD8\u901F\u7F13\u5B58\t\n-codebase <url>\t\u4ECE\u7ED9\u5B9A\u7684\u4EE3\u7801\u5E93\u68C0\u7D22\u8D44\u6E90\t\n-shortcut      \t\u4EE5\u7528\u6237\u63A5\u53D7\u63D0\u793A\u7684\u65B9\u5F0F\u5B89\u88C5\u5FEB\u6377\u65B9\u5F0F\t\n-association   \t\u4EE5\u7528\u6237\u63A5\u53D7\u63D0\u793A\u7684\u65B9\u5F0F\u5B89\u88C5\u5173\u8054\t\n\n
