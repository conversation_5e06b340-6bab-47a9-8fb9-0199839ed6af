#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
晶晨机顶盒签名分析工具包
专门用于分析和逆向system分区的签名验证机制

Author: By.举个🌰
Date: 2025
Copyright: By.举个🌰
"""

import os
import sys
import hashlib
import binascii
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from cryptography import x509
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric import rsa, padding
import zipfile

class SignatureAnalysisToolkit:
    """签名分析工具包"""
    
    def __init__(self, work_dir: str = "."):
        self.work_dir = Path(work_dir)
        self.system_dir = self.work_dir / "system"
        self.analysis_results = {}
        
    def analyze_ota_certificates(self) -> Dict:
        """分析OTA证书"""
        print("🔐 分析OTA证书...")
        
        cert_analysis = {
            "certificates": [],
            "public_keys": [],
            "signature_algorithms": [],
            "validity_periods": [],
            "issuers": []
        }
        
        otacerts_path = self.system_dir / "system" / "etc" / "security" / "otacerts.zip"
        
        if not otacerts_path.exists():
            print("❌ otacerts.zip 文件不存在")
            return cert_analysis
        
        # 提取证书
        extract_dir = self.work_dir / "otacerts_extracted"
        extract_dir.mkdir(exist_ok=True)
        
        try:
            with zipfile.ZipFile(otacerts_path, 'r') as zip_ref:
                zip_ref.extractall(extract_dir)
            
            # 分析每个证书文件
            for cert_file in extract_dir.glob("*.pem"):
                cert_info = self._analyze_certificate(cert_file)
                if cert_info:
                    cert_analysis["certificates"].append(cert_info)
                    
        except Exception as e:
            print(f"❌ 证书分析失败: {e}")
        
        return cert_analysis
    
    def _analyze_certificate(self, cert_path: Path) -> Optional[Dict]:
        """分析单个证书文件"""
        try:
            with open(cert_path, 'rb') as f:
                cert_data = f.read()
            
            # 解析证书
            cert = x509.load_pem_x509_certificate(cert_data)
            
            # 提取证书信息
            cert_info = {
                "file_name": cert_path.name,
                "subject": str(cert.subject),
                "issuer": str(cert.issuer),
                "serial_number": str(cert.serial_number),
                "not_valid_before": cert.not_valid_before.isoformat(),
                "not_valid_after": cert.not_valid_after.isoformat(),
                "signature_algorithm": cert.signature_algorithm_oid._name,
                "public_key_algorithm": cert.public_key().__class__.__name__,
                "public_key_size": None,
                "public_key_hex": None
            }
            
            # 获取公钥信息
            public_key = cert.public_key()
            if isinstance(public_key, rsa.RSAPublicKey):
                cert_info["public_key_size"] = public_key.key_size
                
                # 提取公钥的十六进制表示
                public_key_der = public_key.public_bytes(
                    encoding=serialization.Encoding.DER,
                    format=serialization.PublicFormat.SubjectPublicKeyInfo
                )
                cert_info["public_key_hex"] = binascii.hexlify(public_key_der).decode('utf-8')
            
            return cert_info
            
        except Exception as e:
            print(f"❌ 证书 {cert_path.name} 分析失败: {e}")
            return None
    
    def analyze_public_keys(self) -> Dict:
        """分析公钥文件"""
        print("🔑 分析公钥文件...")
        
        key_analysis = {
            "raw_keys": [],
            "key_hashes": [],
            "key_analysis": []
        }
        
        pub_keys_path = self.system_dir / "system" / "etc" / "security" / "pub_keys.txt"
        
        if not pub_keys_path.exists():
            print("❌ pub_keys.txt 文件不存在")
            return key_analysis
        
        try:
            with open(pub_keys_path, 'r', encoding='utf-8') as f:
                lines = f.read().strip().split('\n')
            
            for i, line in enumerate(lines):
                if line.strip():
                    key_hex = line.strip()
                    key_analysis["raw_keys"].append({
                        "index": i + 1,
                        "hex_data": key_hex,
                        "length": len(key_hex),
                        "sha256": hashlib.sha256(key_hex.encode()).hexdigest()
                    })
                    
                    # 尝试解析为RSA公钥
                    key_info = self._analyze_hex_public_key(key_hex)
                    if key_info:
                        key_analysis["key_analysis"].append(key_info)
                        
        except Exception as e:
            print(f"❌ 公钥分析失败: {e}")
        
        return key_analysis
    
    def _analyze_hex_public_key(self, hex_key: str) -> Optional[Dict]:
        """分析十六进制公钥"""
        try:
            # 将十六进制转换为字节
            key_bytes = binascii.unhexlify(hex_key)
            
            # 分析密钥结构
            key_info = {
                "hex_key": hex_key,
                "byte_length": len(key_bytes),
                "sha1": hashlib.sha1(key_bytes).hexdigest(),
                "sha256": hashlib.sha256(key_bytes).hexdigest(),
                "md5": hashlib.md5(key_bytes).hexdigest(),
                "key_type": "Unknown"
            }
            
            # 尝试识别密钥类型
            if len(key_bytes) == 256:  # 2048位RSA公钥
                key_info["key_type"] = "RSA-2048 (推测)"
                key_info["estimated_bits"] = 2048
            elif len(key_bytes) == 512:  # 4096位RSA公钥
                key_info["key_type"] = "RSA-4096 (推测)"
                key_info["estimated_bits"] = 4096
            
            return key_info
            
        except Exception as e:
            print(f"❌ 十六进制公钥解析失败: {e}")
            return None
    
    def analyze_signature_verification_flow(self) -> Dict:
        """分析签名验证流程"""
        print("🔍 分析签名验证流程...")
        
        verification_analysis = {
            "recovery_verification": {},
            "system_verification": {},
            "app_verification": {},
            "ota_verification": {}
        }
        
        # 分析recovery验证
        recovery_patch = self.system_dir / "system" / "recovery-from-boot.p"
        if recovery_patch.exists():
            verification_analysis["recovery_verification"] = {
                "file_exists": True,
                "file_size": recovery_patch.stat().st_size,
                "file_hash": self._calculate_file_hash(recovery_patch)
            }
        
        # 分析应用签名验证配置
        sig_apps_path = self.system_dir / "system" / "etc" / "signatureApplications.xml"
        if sig_apps_path.exists():
            verification_analysis["app_verification"] = self._analyze_signature_apps(sig_apps_path)
        
        return verification_analysis
    
    def _calculate_file_hash(self, file_path: Path) -> Dict[str, str]:
        """计算文件哈希值"""
        hashes_dict = {}
        
        try:
            with open(file_path, 'rb') as f:
                content = f.read()
            
            hashes_dict["md5"] = hashlib.md5(content).hexdigest()
            hashes_dict["sha1"] = hashlib.sha1(content).hexdigest()
            hashes_dict["sha256"] = hashlib.sha256(content).hexdigest()
            
        except Exception as e:
            print(f"❌ 文件哈希计算失败: {e}")
        
        return hashes_dict
    
    def _analyze_signature_apps(self, xml_path: Path) -> Dict:
        """分析签名应用配置"""
        import xml.etree.ElementTree as ET
        
        app_analysis = {
            "configured_apps": [],
            "xml_content": ""
        }
        
        try:
            with open(xml_path, 'r', encoding='utf-8') as f:
                xml_content = f.read()
                app_analysis["xml_content"] = xml_content
            
            tree = ET.parse(xml_path)
            root = tree.getroot()
            
            for key in root.findall('.//key'):
                for pkg in key.findall('packageName'):
                    if pkg.text:
                        app_analysis["configured_apps"].append(pkg.text)
                        
        except Exception as e:
            print(f"❌ 签名应用配置分析失败: {e}")
        
        return app_analysis
    
    def generate_signature_analysis_report(self) -> str:
        """生成签名分析报告"""
        print("📊 生成签名分析报告...")
        
        # 执行各项分析
        ota_analysis = self.analyze_ota_certificates()
        key_analysis = self.analyze_public_keys()
        verification_analysis = self.analyze_signature_verification_flow()
        
        # 生成报告
        report = []
        report.append("=" * 80)
        report.append("晶晨机顶盒签名机制深度分析报告")
        report.append("By.举个🌰")
        report.append("=" * 80)
        report.append("")
        
        # OTA证书分析
        report.append("🔐 OTA证书分析:")
        if ota_analysis["certificates"]:
            for cert in ota_analysis["certificates"]:
                report.append(f"  证书文件: {cert['file_name']}")
                report.append(f"    主题: {cert['subject']}")
                report.append(f"    颁发者: {cert['issuer']}")
                report.append(f"    有效期: {cert['not_valid_before']} 至 {cert['not_valid_after']}")
                report.append(f"    签名算法: {cert['signature_algorithm']}")
                report.append(f"    公钥算法: {cert['public_key_algorithm']}")
                if cert['public_key_size']:
                    report.append(f"    公钥长度: {cert['public_key_size']} bits")
                report.append("")
        
        # 公钥分析
        report.append("🔑 公钥分析:")
        if key_analysis["raw_keys"]:
            for key in key_analysis["raw_keys"]:
                report.append(f"  公钥 {key['index']}:")
                report.append(f"    长度: {key['length']} 字符 ({key['length']//2} 字节)")
                report.append(f"    SHA256: {key['sha256']}")
                report.append("")
        
        if key_analysis["key_analysis"]:
            for key_info in key_analysis["key_analysis"]:
                report.append(f"  密钥类型分析:")
                report.append(f"    推测类型: {key_info['key_type']}")
                report.append(f"    字节长度: {key_info['byte_length']}")
                report.append(f"    SHA1: {key_info['sha1']}")
                report.append("")
        
        # 签名验证流程
        report.append("🔍 签名验证机制:")
        if verification_analysis["app_verification"].get("configured_apps"):
            report.append("  配置的签名应用:")
            for app in verification_analysis["app_verification"]["configured_apps"]:
                report.append(f"    - {app}")
            report.append("")
        
        if verification_analysis["recovery_verification"].get("file_exists"):
            recovery_info = verification_analysis["recovery_verification"]
            report.append("  Recovery验证:")
            report.append(f"    文件大小: {recovery_info['file_size']} 字节")
            if recovery_info.get("file_hash"):
                report.append(f"    SHA256: {recovery_info['file_hash'].get('sha256', 'N/A')}")
            report.append("")
        
        report.append("=" * 80)
        report.append("🎯 关键发现:")
        report.append("1. 设备使用test-keys签名，表明这是开发/测试版本")
        report.append("2. SELinux处于Permissive模式，安全限制较松")
        report.append("3. ro.secure=1但ro.debuggable=1，存在调试接口")
        report.append("4. ADB root权限已开启，便于调试分析")
        report.append("5. 安全补丁级别较旧(2018-08-05)，可能存在已知漏洞")
        report.append("")
        report.append("🔧 逆向建议:")
        report.append("1. 可以通过替换pub_keys.txt中的公钥来绕过签名验证")
        report.append("2. 修改otacerts.zip中的证书可以自签名OTA包")
        report.append("3. 利用test-keys可以重新签名系统应用")
        report.append("4. 通过ADB root权限可以直接修改系统文件")
        report.append("=" * 80)
        
        return "\n".join(report)
    
    def run_comprehensive_analysis(self):
        """运行综合分析"""
        print("🚀 开始签名机制综合分析...")
        
        if not self.system_dir.exists():
            print("❌ system目录不存在")
            return
        
        # 生成报告
        report = self.generate_signature_analysis_report()
        
        # 保存报告
        report_file = self.work_dir / "signature_analysis_report.txt"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        
        print(f"📄 签名分析报告已保存到: {report_file}")
        print("\n" + report)

def main():
    """主函数"""
    print("🔧 晶晨机顶盒签名分析工具包")
    print("Copyright: By.举个🌰")
    print("-" * 50)
    
    # 创建分析器实例
    toolkit = SignatureAnalysisToolkit()
    
    # 运行综合分析
    toolkit.run_comprehensive_analysis()

if __name__ == "__main__":
    main()
