/*
 * Copyright (c) 1999, 2001, Oracle and/or its affiliates. All rights reserved.
 * ORACLE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 */

/*
 * This file contains OMG IDL from  CORBA V2.0, July 1995.
 * It also contains the TypeCode creation APIs in CORBA::ORB
 **/

#pragma prefix "omg.org"

module CORBA {
    typedef string Identifier;
    typedef string ScopedName;
    typedef string RepositoryId;

    enum DefinitionKind {
	dk_none, dk_all,
	dk_Attribute, dk_Constant, dk_Exception, dk_Interface,
	dk_Module, dk_Operation, dk_Typedef,
	dk_Alias, dk_Struct, dk_Union, dk_Enum,
	dk_Primitive, dk_String, dk_Sequence, dk_Array,
	dk_Repository,
	dk_Wstring, dk_Fixed,
	dk_Value, dk_ValueBox, dk_ValueMember, // orbos 98-01-18: Objects By Value
	dk_Native
    };


    interface IRObject
    /**
      An IRObject IDL interface represents the most generic interface
      from which all other Interface Repository interfaces are derived,
      even the Repository itself.
     */
    {
	// read interface
	readonly attribute DefinitionKind def_kind;

	// write interface
	void destroy ();
    };



    typedef string VersionSpec;

    interface Contained;
    interface Repository;
    interface Container;

    interface Contained : IRObject
    /**
       The Contained Interface is inherited by all Interface Repository
       interfaces that are contained by other objects.
     */
    {
	// read/write interface

	attribute RepositoryId id;
	attribute Identifier name;
	attribute VersionSpec version;

	// read interface

	readonly attribute Container defined_in;
	readonly attribute ScopedName absolute_name;
	readonly attribute Repository containing_repository;

	struct Description {
	    DefinitionKind kind;
	    any value;
	};

	Description describe ();

	// write interface

	void move (
	    in Container new_container,
	    in Identifier new_name,
	    in VersionSpec new_version
	    );
    };


    interface ModuleDef;
    interface ConstantDef;
    interface IDLType;
    interface StructDef;
    interface UnionDef;
    interface EnumDef;
    interface AliasDef;
    interface InterfaceDef;
    interface ExceptionDef;
    interface ValueDef;		// orbos 98-01-18: Objects By Value
    interface ValueMemberDef;	// orbos 98-01-18: Objects By Value
    interface ValueBoxDef;	// orbos 98-01-18: Objects By Value
    interface NativeDef;


    typedef sequence <InterfaceDef> InterfaceDefSeq;


    typedef sequence <Contained> ContainedSeq;

    struct StructMember {
	Identifier name;
	TypeCode type;
	IDLType type_def;
    };
    typedef sequence <StructMember> StructMemberSeq;

    struct UnionMember {
	Identifier name;
	any label;
	TypeCode type;
	IDLType type_def;
    };
    typedef sequence <UnionMember> UnionMemberSeq;


    typedef sequence <Identifier> EnumMemberSeq;

    // orbos 98-01-18: Objects By Value -- begin
    typedef short Visibility;	
    const Visibility PRIVATE_MEMBER = 0; 
    const Visibility PUBLIC_MEMBER = 1;

    struct ValueMember {
        Identifier name;
	RepositoryId id;
	RepositoryId defined_in;
	VersionSpec version;
        TypeCode type;
        IDLType type_def;
        Visibility access;
    };
    typedef sequence <ValueMember> ValueMemberSeq;

    struct Initializer {
        StructMemberSeq members;
    };
    typedef sequence <Initializer> InitializerSeq;

    typedef sequence <ValueDef> ValueDefSeq;
    
    // orbos 98-01-18: Objects By Value -- end


    interface Container : IRObject
    /**
      The Container interface is used to form a containment hierarchy
      in the Interface Repository. A Container can contain any number
      of objects derived from the Contained interface.
    */
    {
	// read interface

	Contained lookup ( in ScopedName search_name);

	ContainedSeq contents (
	    in DefinitionKind limit_type,
	    in boolean exclude_inherited
	    );

	ContainedSeq lookup_name (
	    in Identifier search_name,
	    in long levels_to_search,
	    in DefinitionKind limit_type,
	    in boolean exclude_inherited
	    );

	struct Description {
	    Contained contained_object;
	    DefinitionKind kind;
	    any value;
	};

	typedef sequence<Description> DescriptionSeq;

	DescriptionSeq describe_contents (
	    in DefinitionKind limit_type,
	    in boolean exclude_inherited,
	    in long max_returned_objs
	    );

	// write interface

	ModuleDef create_module (
	    in RepositoryId id,
	    in Identifier name,
	    in VersionSpec version
	    );

	ConstantDef create_constant (
	    in RepositoryId id,
	    in Identifier name,
	    in VersionSpec version,
	    in IDLType type,
	    in any value
	    );

	StructDef create_struct (
	    in RepositoryId id,
	    in Identifier name,
	    in VersionSpec version,
	    in StructMemberSeq members
	    );

	UnionDef create_union (
	    in RepositoryId id,
	    in Identifier name,
	    in VersionSpec version,
	    in IDLType discriminator_type,
	    in UnionMemberSeq members
	    );

	EnumDef create_enum (
	    in RepositoryId id,
	    in Identifier name,
	    in VersionSpec version,
	    in EnumMemberSeq members
	    );

	AliasDef create_alias (
	    in RepositoryId id,
	    in Identifier name,
	    in VersionSpec version,
	    in IDLType original_type
	    );

        ExceptionDef create_exception (
            in RepositoryId    id,
            in Identifier      name,
            in VersionSpec     version,
            in StructMemberSeq members
        );


	InterfaceDef create_interface (
	    in RepositoryId id,
	    in Identifier name,
	    in VersionSpec version,
	    in boolean is_abstract,
	    in InterfaceDefSeq base_interfaces
	    );

	// orbos 98-01-18: Objects By Value
	ValueDef create_value(
	    in RepositoryId id,
	    in Identifier name,
	    in VersionSpec version,
	    in boolean is_custom,
	    in boolean is_abstract,
	    in octet flags,	// must be 0
	    in ValueDef base_value,
	    in boolean has_safe_base,
	    in ValueDefSeq abstract_base_values,
	    in InterfaceDefSeq supported_interfaces,
	    in InitializerSeq initializers
	    );
	
	// orbos 98-01-18: Objects By Value
	ValueBoxDef create_value_box(
	    in RepositoryId id,
	    in Identifier name,
	    in VersionSpec version,
	    in IDLType original_type_def
	    );
	
	NativeDef create_native(
	    in RepositoryId id,
	    in Identifier name,
	    in VersionSpec version
	    );
	
    };



    interface IDLType : IRObject
    /**
       The IDLType interface is an abstract interface inherited by all
       IR objects that represent the OMG IDL types. It provides access
       to the TypeCode describing the type, and is used in defining the
       other interfaces wherever definitions of IDLType must be referenced.
    */
    {
	readonly attribute TypeCode type;
    };



    interface PrimitiveDef;
    interface StringDef;
    interface SequenceDef;
    interface ArrayDef;

    enum PrimitiveKind {
	pk_null, pk_void, pk_short, pk_long, pk_ushort, pk_ulong,
	pk_float, pk_double, pk_boolean, pk_char, pk_octet,
	pk_any, pk_TypeCode, pk_Principal, pk_string, pk_objref
    };

    interface Repository : Container
    /**
      Repository is an interface that provides global access to the
      Interface Repository. Repository objects can contain constants,
      typedefs, exceptions, interfaces, and modules.
    */
    {
	// read interface

	Contained lookup_id (in RepositoryId search_id);

	PrimitiveDef get_primitive (in PrimitiveKind kind);

	// write interface

	StringDef create_string (in unsigned long bound);

	SequenceDef create_sequence (
	    in unsigned long bound,
	    in IDLType element_type
	    );

	ArrayDef create_array (
	    in unsigned long length,
	    in IDLType element_type
	    );
    };


    interface ModuleDef : Container, Contained
    /**
      A ModuleDef can contain constants, typedefs, exceptions, interfaces,
      and other module objects.
    */
    {
    };

    struct ModuleDescription {
	Identifier name;
	RepositoryId id;
	RepositoryId defined_in;
	VersionSpec version;
    };


    interface ConstantDef : Contained
    /**
      A ConstantDef object defines a named constant.
    */
    {
	readonly attribute TypeCode type;
	attribute IDLType type_def;
	attribute any value;
    };

    struct ConstantDescription {
	Identifier name;
	RepositoryId id;
	RepositoryId defined_in;
	VersionSpec version;
	TypeCode type;
	any value;
    };


    interface TypedefDef : Contained, IDLType
    /**
      TypedefDef is an abstract interface used as a base interface for
      all named non-object types(structures, unions, enumerations,
      aliases). The TypedefDef interface is not inherited by the definition
      objects for the primitive or anonymous types.
    */
    {
    };

    struct TypeDescription {
	Identifier name;
	RepositoryId id;
	RepositoryId defined_in;
	VersionSpec version;
	TypeCode type;
    };


    interface StructDef : TypedefDef, Container
    /**
       A StructDef represents an OMG IDL structure definition.
    */
    {
	attribute StructMemberSeq members;
    };


    interface UnionDef : TypedefDef, Container
    /**
       A UnionDef represents an OMG IDL union definition.
     */
    {
	readonly attribute TypeCode discriminator_type;
	attribute IDLType discriminator_type_def;
	attribute UnionMemberSeq members;
    };


    interface EnumDef : TypedefDef
    /**
      A EnumDef represents an OMG IDL enum definition.
     */
    {
	attribute EnumMemberSeq members;
    };


    interface AliasDef : TypedefDef
    /**
       An AliasDef represents an OMG IDL typedef that aliases other
       definition.
    */
    {
	attribute IDLType original_type_def;
    };


    interface PrimitiveDef: IDLType
    /**
      A PrimitiveDef represents one of the IDL primitive types. As
      primitive types are unnamed, this interface is not derived from
      TypedefDef or Contained.
     */
    {
	readonly attribute PrimitiveKind kind;
    };


    interface StringDef : IDLType
    /**
      A StringDef represents an OMG IDL string type. As string
      types are anonymous, this interface is not derived from TypedefDef
      or Contained.
    */
    {
	attribute unsigned long bound;
    };


    interface SequenceDef : IDLType
    /**
      A SequenceDef represents an OMG IDL sequence type. As sequence
      types are anonymous, this interface is not derived from TypedefDef
      or Contained.
     */
    {
	attribute unsigned long bound;
	readonly attribute TypeCode element_type;
	attribute IDLType element_type_def;
    };

    interface ArrayDef : IDLType
    /**
      An ArrayDef represents an OMG IDL array type. As array
      types are anonymous, this interface is not derived from TypedefDef
      or Contained.
    */
    {
	attribute unsigned long length;
	readonly attribute TypeCode element_type;
	attribute IDLType element_type_def;
    };


    interface ExceptionDef : Contained, Container
    /**
      An ExceptionDef represents an exception definition.
    */
    {
	readonly attribute TypeCode type;
	attribute StructMemberSeq members;
    };
    struct ExceptionDescription {
	Identifier name;
	RepositoryId id;
	RepositoryId defined_in;
	VersionSpec version;
	TypeCode type;
    };



    enum AttributeMode {ATTR_NORMAL, ATTR_READONLY};

    interface AttributeDef : Contained
    /**
      An AttributeDef represents the information that defines an
      attribute of an interface.
    */
    {
	readonly attribute TypeCode type;
	attribute IDLType type_def;
	attribute AttributeMode mode;
    };

    struct AttributeDescription {
	Identifier name;
	RepositoryId id;
	RepositoryId defined_in;
	VersionSpec version;
	TypeCode type;
	AttributeMode mode;
    };



    enum OperationMode {OP_NORMAL, OP_ONEWAY};

    enum ParameterMode {PARAM_IN, PARAM_OUT, PARAM_INOUT};
    struct ParameterDescription {
	Identifier name;
	TypeCode type;
	IDLType type_def;
	ParameterMode mode;
    };
    typedef sequence <ParameterDescription> ParDescriptionSeq;

    typedef Identifier ContextIdentifier;
    typedef sequence <ContextIdentifier> ContextIdSeq;

    typedef sequence <ExceptionDef> ExceptionDefSeq;
    typedef sequence <ExceptionDescription> ExcDescriptionSeq;

    interface OperationDef : Contained
    /**
      An OperationDef represents the information that defines an
      operation of an interface.
     */
    {
	readonly attribute TypeCode result;
	attribute IDLType result_def;
	attribute ParDescriptionSeq params;
	attribute OperationMode mode;
	attribute ContextIdSeq contexts;
	attribute ExceptionDefSeq exceptions;
    };

    struct OperationDescription {
	Identifier name;
	RepositoryId id;
	RepositoryId defined_in;
	VersionSpec version;
	TypeCode result;
	OperationMode mode;
	ContextIdSeq contexts;
	ParDescriptionSeq parameters;
	ExcDescriptionSeq exceptions;
    };



    typedef sequence <RepositoryId> RepositoryIdSeq;
    typedef sequence <OperationDescription> OpDescriptionSeq;
    typedef sequence <AttributeDescription> AttrDescriptionSeq;

    interface InterfaceDef : Container, Contained, IDLType
    /**
      An InterfaceDef object represents an interface definition. It can
      contains constants, typedefs, exceptions, operations, and
      attributes.
     */
    {
	// read/write interface

	attribute InterfaceDefSeq base_interfaces;
	attribute boolean is_abstract;

	// read interface

	boolean is_a (in RepositoryId interface_id);

	struct FullInterfaceDescription {
	    Identifier name;
	    RepositoryId id;
	    RepositoryId defined_in;
	    VersionSpec version;
	    boolean is_abstract;
	    OpDescriptionSeq operations;
	    AttrDescriptionSeq attributes;
	    RepositoryIdSeq base_interfaces;
	    TypeCode type;
	};

	FullInterfaceDescription describe_interface();

	// write interface

	AttributeDef create_attribute (
	    in RepositoryId id,
	    in Identifier name,
	    in VersionSpec version,
	    in IDLType type,
	    in AttributeMode mode
	    );

	OperationDef create_operation (
	    in RepositoryId id,
	    in Identifier name,
	    in VersionSpec version,
	    in IDLType result,
	    in OperationMode mode,
	    in ParDescriptionSeq params,
	    in ExceptionDefSeq exceptions,
	    in ContextIdSeq contexts
	    );
    };

    struct InterfaceDescription {
	Identifier name;
	RepositoryId id;
	RepositoryId defined_in;
	VersionSpec version;
	RepositoryIdSeq base_interfaces;
    };


    // orbos 98-01-18: Objects By Value -- begin

    interface ValueMemberDef : Contained

      /** A <code>ValueMemberDef</code> object represents the public
      and private data member definition of a <code>Value</code> type
      */

 {
        readonly attribute TypeCode type;
	         attribute IDLType type_def;
		 attribute Visibility access;
    };

    interface ValueDef : Container, Contained, IDLType
      /**
	A ValueDef object represents the definition of the
	<code>Value</code> object used to pass the object state
	between hosts
	*/

      {
	   // read/write interface
	    attribute InterfaceDefSeq supported_interfaces;
	    attribute InitializerSeq initializers;
	    attribute ValueDef base_value;
	    attribute ValueDefSeq abstract_base_values;
	    attribute boolean is_abstract;
	    attribute boolean is_custom;
	    attribute octet flags; // always 0
	    attribute boolean has_safe_base;

	    // read interface
	    boolean is_a(in RepositoryId value_id);

	    struct FullValueDescription {
	      Identifier 	name;
	      RepositoryId 	id;
	      boolean 	is_abstract;
	      boolean 	is_custom;
	      octet 		flags; // always 0
	      RepositoryId 	defined_in;
	      VersionSpec 	version;
	      OpDescriptionSeq operations;
	      AttrDescriptionSeq attributes;
	      ValueMemberSeq 	members;
	      InitializerSeq 	initializers;
	      RepositoryIdSeq supported_interfaces;
	      RepositoryIdSeq abstract_base_values;
	      boolean 	has_safe_base;
	      RepositoryId	base_value;
	      TypeCode	type;
	  };

	  FullValueDescription describe_value();

	  // write interface

	  ValueMemberDef create_value_member(
	      in RepositoryId id,
	      in Identifier name,
	      in VersionSpec version,
	      in IDLType type_def,
	      in Visibility access
	  );

	  AttributeDef create_attribute(
	      in RepositoryId id,
	      in Identifier   name,
	      in VersionSpec  version,
	      in IDLType      type,
	      in AttributeMode mode
	  );

	  OperationDef create_operation(
	      in RepositoryId id,
	      in Identifier   name,
	      in VersionSpec  version,
	      in IDLType      result,
	      in OperationMode     mode,
	      in ParDescriptionSeq params,
	      in ExceptionDefSeq   exceptions,
	      in ContextIdSeq contexts
	  );
    };
    struct ValueDescription {
        Identifier name;
        RepositoryId id;
        boolean is_abstract;
        boolean is_custom;
        octet   flags; // always 0
        RepositoryId defined_in;
        VersionSpec version;
        RepositoryIdSeq supported_interfaces;
        RepositoryIdSeq abstract_base_values;
        boolean has_safe_base;
        RepositoryId base_value;
    };

    interface ValueBoxDef : IDLType 

      /** ValueBoxDef is an interface that reresents a value type with
	a single data member inside its state section and no
	inheritance or methods. For example, when transmitting a
	string or sequence as an actual parameter on an interface
	operation or as a data member of a value type that is an
	actual parameter, it may be important to preserve any sharing
	of the string or sequence within the object graph being
	transmitted. Because current IDL data types do not preserve
	referential integrity in this way, this requirement is
	conveniently handled by using a value type. Value types also
	support the transmission of nulls (as a distinguished value),
	whereas IDL data types such as string and sequence (which are
	mapped to empty strings and sequences) do not. The Java to IDL
	mapping requires both preservation of referential integrity
	and transmission of nulls. Because it would be cumbersome to
	require the full IDL syntax for a value type for this specific
	usage, this shorthand notation is introduced to cover this use
	of value types for simple containment of a single data member.
	*/

{
        attribute IDLType original_type_def;
    };
    
    // orbos 98-01-18: Objects By Value -- end

    interface NativeDef : TypedefDef {
    };
};
