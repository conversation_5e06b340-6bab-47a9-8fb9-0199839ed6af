<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2009 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at
  
          http://www.apache.org/licenses/LICENSE-2.0
  
     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<!-- This feature should be defined for devices that support live wallpapers.
     There are minimum hardware requirements to be able to support this
     feature: robust multiple GL context support, fast enough CPU, enough
     RAM to allow the wallpaper to be running all of the time. -->
<permissions>
    <feature name="android.software.live_wallpaper" />
</permissions>
