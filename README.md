# 晶晨机顶盒固件逆向分析工具包

**By.举个🌰**

专门用于分析晶晨(Amlogic)机顶盒固件系统卡刷包的签名机制和安全特性的逆向分析工具集。

## 🎯 项目特性

- **固件基础分析**: 解析设备信息、系统结构和应用统计
- **ADB调试分析**: 通过ADB连接获取运行时安全信息
- **签名机制深度分析**: 分析OTA证书、公钥和签名验证流程
- **虚拟环境隔离**: 所有依赖在独立虚拟环境中运行
- **一键式分析**: 提供批处理和PowerShell启动脚本

## 📋 环境要求

- Python 3.7+
- ADB工具 (用于设备调试)
- Windows PowerShell (推荐)

## 🚀 快速开始

### 方法1: 使用PowerShell脚本 (推荐)

```powershell
# 运行PowerShell启动器
.\run_analysis.ps1
```

### 方法2: 使用批处理脚本

```cmd
# 运行批处理启动器
run_analysis.bat
```

### 方法3: 手动运行

```powershell
# 创建虚拟环境
python -m venv firmware_analysis_env

# 激活虚拟环境
.\firmware_analysis_env\Scripts\Activate.ps1

# 安装依赖
pip install cryptography

# 运行分析工具
python firmware_reverse_assistant.py
python adb_debug_analyzer.py
python signature_analysis_toolkit.py
```

## 🔧 工具说明

### 1. firmware_reverse_assistant.py
**固件基础分析工具**

- 分析设备基本信息 (型号、制造商、Android版本等)
- 解析系统结构 (应用数量、库文件统计等)
- 提取签名验证机制配置
- 生成综合分析报告

**输出文件:**
- `firmware_analysis_report.txt` - 分析报告
- `analysis_results.json` - 详细JSON数据

### 2. adb_debug_analyzer.py
**ADB调试分析工具**

- 通过ADB连接获取系统属性
- 分析安全相关配置
- 检查签名验证状态
- 获取运行进程和已安装应用信息

**输出文件:**
- `adb_debug_report.txt` - 调试报告
- `adb_analysis_data.json` - 详细调试数据

### 3. signature_analysis_toolkit.py
**签名机制深度分析工具**

- 解析OTA证书 (otacerts.zip)
- 分析公钥文件 (pub_keys.txt)
- 研究签名验证流程
- 提供逆向建议和安全评估

**输出文件:**
- `signature_analysis_report.txt` - 签名分析报告

## 📁 项目结构

```
.
├── firmware_reverse_assistant.py    # 固件基础分析工具
├── adb_debug_analyzer.py            # ADB调试分析工具
├── signature_analysis_toolkit.py    # 签名机制分析工具
├── run_analysis.ps1                 # PowerShell启动脚本
├── run_analysis.bat                 # 批处理启动脚本
├── README.md                        # 项目说明文档
├── firmware_analysis_env/           # Python虚拟环境
├── system/                          # 解压的系统文件
├── update.zip                       # 原始卡刷包
└── otacerts_extracted/              # 提取的证书文件
```

## 🔍 分析目标

本工具包专门针对以下设备进行优化:

- **设备型号**: CM311-1-ALL (晶晨S905L3芯片)
- **Android版本**: 9.0.0 (API 28)
- **制造商**: CMDC (中国移动)
- **构建类型**: userdebug (test-keys)

## 🎯 关键发现

通过分析发现的重要安全特性:

1. **test-keys签名**: 设备使用测试密钥，便于逆向分析
2. **SELinux Permissive**: 安全限制较松，有利于调试
3. **ADB Root权限**: 已开启root权限，可直接修改系统
4. **签名验证机制**: 可通过替换公钥绕过验证
5. **OTA证书**: 使用自签名证书，可制作自定义OTA包

## 🔧 逆向建议

1. **绕过签名验证**: 替换 `pub_keys.txt` 中的公钥
2. **自签名OTA包**: 修改 `otacerts.zip` 中的证书
3. **重新签名应用**: 利用test-keys重新签名系统应用
4. **直接修改系统**: 通过ADB root权限修改系统文件

## ⚠️ 免责声明

本工具包仅用于学习和研究目的。使用者应遵守相关法律法规，不得用于非法用途。作者不承担任何法律责任。

## 📄 版权信息

Copyright © 2025 By.举个🌰

本项目采用教育和研究用途许可。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个工具包。

## 📞 联系方式

如有问题或建议，请通过GitHub Issues联系。

---

**Happy Reversing! 🎉**
