# 晶晨机顶盒固件逆向分析工具启动器
# By.举个🌰

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "晶晨机顶盒固件逆向分析工具启动器" -ForegroundColor Yellow
Write-Host "By.举个🌰" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# 检查虚拟环境是否存在
if (-not (Test-Path "firmware_analysis_env\Scripts\Activate.ps1")) {
    Write-Host "创建虚拟环境..." -ForegroundColor Yellow
    python -m venv firmware_analysis_env
    Write-Host "虚拟环境创建完成" -ForegroundColor Green
    Write-Host ""
}

# 激活虚拟环境
Write-Host "激活虚拟环境..." -ForegroundColor Yellow
& .\firmware_analysis_env\Scripts\Activate.ps1

# 安装依赖
Write-Host "检查并安装依赖..." -ForegroundColor Yellow
pip install cryptography | Out-Null

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "选择要运行的分析工具:" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "1. 固件基础分析 (firmware_reverse_assistant.py)" -ForegroundColor White
Write-Host "2. ADB调试分析 (adb_debug_analyzer.py)" -ForegroundColor White
Write-Host "3. 签名机制深度分析 (signature_analysis_toolkit.py)" -ForegroundColor White
Write-Host "4. 运行所有分析工具" -ForegroundColor White
Write-Host "5. 退出" -ForegroundColor White
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

$choice = Read-Host "请输入选择 (1-5)"

switch ($choice) {
    "1" {
        Write-Host ""
        Write-Host "运行固件基础分析..." -ForegroundColor Green
        python firmware_reverse_assistant.py
        Read-Host "按回车键继续..."
    }
    "2" {
        Write-Host ""
        Write-Host "运行ADB调试分析..." -ForegroundColor Green
        python adb_debug_analyzer.py
        Read-Host "按回车键继续..."
    }
    "3" {
        Write-Host ""
        Write-Host "运行签名机制深度分析..." -ForegroundColor Green
        python signature_analysis_toolkit.py
        Read-Host "按回车键继续..."
    }
    "4" {
        Write-Host ""
        Write-Host "运行所有分析工具..." -ForegroundColor Green
        Write-Host ""
        Write-Host "[1/3] 固件基础分析..." -ForegroundColor Yellow
        python firmware_reverse_assistant.py
        Write-Host ""
        Write-Host "[2/3] ADB调试分析..." -ForegroundColor Yellow
        python adb_debug_analyzer.py
        Write-Host ""
        Write-Host "[3/3] 签名机制深度分析..." -ForegroundColor Yellow
        python signature_analysis_toolkit.py
        Write-Host ""
        Write-Host "所有分析完成！" -ForegroundColor Green
        Read-Host "按回车键继续..."
    }
    "5" {
        Write-Host "退出..." -ForegroundColor Yellow
        exit
    }
    default {
        Write-Host "无效选择，请重新运行脚本" -ForegroundColor Red
        Read-Host "按回车键继续..."
    }
}

Write-Host ""
Write-Host "虚拟环境仍处于激活状态，你可以继续使用Python工具" -ForegroundColor Green
Write-Host "要退出虚拟环境，请输入: deactivate" -ForegroundColor Yellow
