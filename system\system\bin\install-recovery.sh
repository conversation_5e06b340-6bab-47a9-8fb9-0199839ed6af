#!/system/bin/sh
if ! applypatch -c EMMC:/dev/block/recovery:15316992:5a894ae042d94087d69cdd3e48aa67e4a4c52fc4; then
  applypatch  EMMC:/dev/block/boot:9406464:d39047c0e660a1e2de1591503399c5c3b07fec9b EMMC:/dev/block/recovery 5a894ae042d94087d69cdd3e48aa67e4a4c52fc4 15316992 d39047c0e660a1e2de1591503399c5c3b07fec9b:/system/recovery-from-boot.p && log -t recovery "Installing new recovery image: succeeded" || log -t recovery "Installing new recovery image: failed"
else
  log -t recovery "Recovery image already installed"
fi
