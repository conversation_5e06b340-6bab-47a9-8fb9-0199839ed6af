<?xml version="1.0" encoding="UTF-8"?>
<project name="itms">
    <!--extend-type platform字段配置的值必须与module platform内的component对应-->
    <extend-type name="platform">sunniwell</extend-type>
    <!--data_manager目前支持两种方式STBManager、ApiManager-->
    <extend-type name="data_manager">ApiManager</extend-type>
    <!--网管0Boot时是否要自启动-->
    <extend-type name="self_start">true</extend-type>
    <!--省码-->
    <extend-type name="publish_region">cmcc_js</extend-type>
    <!--params局点定制目录-->
    <extend-type name="params_folder_name">cmcc_js</extend-type>
    <module name="platform">
        <component name="sunniwell">
            <intent name="data_path">templates/data/cmcc/js/data.xml</intent>
            <intent name="config_path">config/cmcc/js/config.xml</intent>
        </component>
    </module>
</project>