#line 1 "system/sepolicy/private/property_contexts"
##########################
# property service keys
#
#
net.rmnet               u:object_r:net_radio_prop:s0
net.gprs                u:object_r:net_radio_prop:s0
net.ppp                 u:object_r:net_radio_prop:s0
net.qmi                 u:object_r:net_radio_prop:s0
net.lte                 u:object_r:net_radio_prop:s0
net.cdma                u:object_r:net_radio_prop:s0
net.dns                 u:object_r:net_dns_prop:s0
sys.usb.config          u:object_r:system_radio_prop:s0
ril.                    u:object_r:radio_prop:s0
ro.ril.                 u:object_r:radio_prop:s0
gsm.                    u:object_r:radio_prop:s0
persist.radio           u:object_r:radio_prop:s0

net.                    u:object_r:system_prop:s0
dev.                    u:object_r:system_prop:s0
ro.runtime.             u:object_r:system_prop:s0
ro.runtime.firstboot    u:object_r:firstboot_prop:s0
hw.                     u:object_r:system_prop:s0
ro.hw.                  u:object_r:system_prop:s0
sys.                    u:object_r:system_prop:s0
sys.cppreopt            u:object_r:cppreopt_prop:s0
sys.powerctl            u:object_r:powerctl_prop:s0
sys.usb.ffs.            u:object_r:ffs_prop:s0
service.                u:object_r:system_prop:s0
dhcp.                   u:object_r:dhcp_prop:s0
dhcp.bt-pan.result      u:object_r:pan_result_prop:s0
bluetooth.              u:object_r:bluetooth_prop:s0

debug.                  u:object_r:debug_prop:s0
debug.db.               u:object_r:debuggerd_prop:s0
dumpstate.              u:object_r:dumpstate_prop:s0
dumpstate.options       u:object_r:dumpstate_options_prop:s0
log.                    u:object_r:log_prop:s0
log.tag                 u:object_r:log_tag_prop:s0
log.tag.WifiHAL         u:object_r:wifi_log_prop:s0
security.perf_harden    u:object_r:shell_prop:s0
service.adb.root        u:object_r:shell_prop:s0
service.adb.tcp.port    u:object_r:shell_prop:s0

persist.audio.          u:object_r:audio_prop:s0
persist.bluetooth.      u:object_r:bluetooth_prop:s0
persist.debug.          u:object_r:persist_debug_prop:s0
persist.logd.           u:object_r:logd_prop:s0
ro.logd.                u:object_r:logd_prop:s0
persist.logd.security   u:object_r:device_logging_prop:s0
persist.logd.logpersistd        u:object_r:logpersistd_logging_prop:s0
logd.logpersistd        u:object_r:logpersistd_logging_prop:s0
persist.log.tag         u:object_r:log_tag_prop:s0
persist.mmc.            u:object_r:mmc_prop:s0
persist.netd.stable_secret      u:object_r:netd_stable_secret_prop:s0
persist.sys.            u:object_r:system_prop:s0
persist.sys.safemode    u:object_r:safemode_prop:s0
ro.sys.safemode         u:object_r:safemode_prop:s0
persist.sys.audit_safemode      u:object_r:safemode_prop:s0
persist.service.        u:object_r:system_prop:s0
persist.service.bdroid. u:object_r:bluetooth_prop:s0
persist.security.       u:object_r:system_prop:s0
persist.traced.enable   u:object_r:traced_enabled_prop:s0
persist.vendor.overlay.  u:object_r:overlay_prop:s0
ro.boot.vendor.overlay.  u:object_r:overlay_prop:s0
ro.boottime.             u:object_r:boottime_prop:s0
ro.serialno             u:object_r:serialno_prop:s0
ro.boot.btmacaddr       u:object_r:bluetooth_prop:s0
ro.boot.serialno        u:object_r:serialno_prop:s0
ro.bt.                  u:object_r:bluetooth_prop:s0
ro.boot.bootreason      u:object_r:bootloader_boot_reason_prop:s0
persist.sys.boot.reason u:object_r:last_boot_reason_prop:s0
sys.boot.reason         u:object_r:system_boot_reason_prop:s0
pm.                     u:object_r:pm_prop:s0
test.sys.boot.reason    u:object_r:test_boot_reason_prop:s0

# Boolean property set by system server upon boot indicating
# if device owner is provisioned.
ro.device_owner         u:object_r:device_logging_prop:s0

# selinux non-persistent properties
selinux.restorecon_recursive   u:object_r:restorecon_prop:s0

# default property context
*                       u:object_r:default_prop:s0

# data partition encryption properties
vold.                   u:object_r:vold_prop:s0
ro.crypto.              u:object_r:vold_prop:s0

# ro.build.fingerprint is either set in /system/build.prop, or is
# set at runtime by system_server.
ro.build.fingerprint    u:object_r:fingerprint_prop:s0

ro.persistent_properties.ready  u:object_r:persistent_properties_ready_prop:s0

# ctl properties
ctl.bootanim            u:object_r:ctl_bootanim_prop:s0
ctl.android.hardware.dumpstate u:object_r:ctl_dumpstate_prop:s0
ctl.dumpstate           u:object_r:ctl_dumpstate_prop:s0
ctl.fuse_               u:object_r:ctl_fuse_prop:s0
ctl.mdnsd               u:object_r:ctl_mdnsd_prop:s0
ctl.ril-daemon          u:object_r:ctl_rildaemon_prop:s0
ctl.bugreport           u:object_r:ctl_bugreport_prop:s0
ctl.console             u:object_r:ctl_console_prop:s0
ctl.                    u:object_r:ctl_default_prop:s0

# Don't allow blind access to all services
ctl.sigstop_on$         u:object_r:ctl_sigstop_prop:s0
ctl.sigstop_off$        u:object_r:ctl_sigstop_prop:s0
ctl.start$              u:object_r:ctl_start_prop:s0
ctl.stop$               u:object_r:ctl_stop_prop:s0
ctl.restart$            u:object_r:ctl_restart_prop:s0
ctl.interface_start$    u:object_r:ctl_interface_start_prop:s0
ctl.interface_stop$     u:object_r:ctl_interface_stop_prop:s0
ctl.interface_restart$  u:object_r:ctl_interface_restart_prop:s0

# NFC properties
nfc.                    u:object_r:nfc_prop:s0

# These properties are not normally set by processes other than init.
# They are only distinguished here for setting by qemu-props on the
# emulator/goldfish.
config.                 u:object_r:config_prop:s0
ro.config.              u:object_r:config_prop:s0
dalvik.                 u:object_r:dalvik_prop:s0
ro.dalvik.              u:object_r:dalvik_prop:s0

# Shared between system server and wificond
wlan.                   u:object_r:wifi_prop:s0

# Lowpan properties
lowpan.                 u:object_r:lowpan_prop:s0
ro.lowpan.              u:object_r:lowpan_prop:s0

# hwservicemanager properties
hwservicemanager.       u:object_r:hwservicemanager_prop:s0

# Common default properties for vendor and odm.
init.svc.odm.           u:object_r:vendor_default_prop:s0
init.svc.vendor.        u:object_r:vendor_default_prop:s0
ro.hardware.            u:object_r:vendor_default_prop:s0
ro.odm.                 u:object_r:vendor_default_prop:s0
ro.vendor.              u:object_r:vendor_default_prop:s0
odm.                    u:object_r:vendor_default_prop:s0
persist.odm.            u:object_r:vendor_default_prop:s0
persist.vendor.         u:object_r:vendor_default_prop:s0
vendor.                 u:object_r:vendor_default_prop:s0
#line 1 "system/sepolicy/public/property_contexts"
# vendor-init-readable
persist.radio.airplane_mode_on u:object_r:exported2_radio_prop:s0 exact int

# vendor-init-settable
af.fast_track_multiplier u:object_r:exported3_default_prop:s0 exact int
camera.disable_zsl_mode u:object_r:exported3_default_prop:s0 exact bool
camera.fifo.disable u:object_r:exported3_default_prop:s0 exact int
dalvik.vm.appimageformat u:object_r:exported_dalvik_prop:s0 exact string
dalvik.vm.backgroundgctype u:object_r:exported_dalvik_prop:s0 exact string
dalvik.vm.checkjni u:object_r:exported_dalvik_prop:s0 exact bool
dalvik.vm.dex2oat-Xms u:object_r:exported_dalvik_prop:s0 exact string
dalvik.vm.dex2oat-Xmx u:object_r:exported_dalvik_prop:s0 exact string
dalvik.vm.dex2oat-filter u:object_r:exported_dalvik_prop:s0 exact string
dalvik.vm.dex2oat-flags u:object_r:exported_dalvik_prop:s0 exact string
dalvik.vm.dex2oat-threads u:object_r:exported_dalvik_prop:s0 exact int
dalvik.vm.dexopt.secondary u:object_r:exported_dalvik_prop:s0 exact bool
dalvik.vm.execution-mode u:object_r:exported_dalvik_prop:s0 exact string
dalvik.vm.extra-opts u:object_r:exported_dalvik_prop:s0 exact string
dalvik.vm.gctype u:object_r:exported_dalvik_prop:s0 exact string
dalvik.vm.heapgrowthlimit u:object_r:exported_dalvik_prop:s0 exact string
dalvik.vm.heapmaxfree u:object_r:exported_dalvik_prop:s0 exact string
dalvik.vm.heapminfree u:object_r:exported_dalvik_prop:s0 exact string
dalvik.vm.heapsize u:object_r:exported_dalvik_prop:s0 exact string
dalvik.vm.heapstartsize u:object_r:exported_dalvik_prop:s0 exact string
dalvik.vm.heaptargetutilization u:object_r:exported_dalvik_prop:s0 exact string
dalvik.vm.hot-startup-method-samples u:object_r:exported_dalvik_prop:s0 exact int
dalvik.vm.image-dex2oat-Xms u:object_r:exported_dalvik_prop:s0 exact string
dalvik.vm.image-dex2oat-Xmx u:object_r:exported_dalvik_prop:s0 exact string
dalvik.vm.image-dex2oat-filter u:object_r:exported_dalvik_prop:s0 exact string
dalvik.vm.image-dex2oat-flags u:object_r:exported_dalvik_prop:s0 exact string
dalvik.vm.image-dex2oat-threads u:object_r:exported_dalvik_prop:s0 exact int
dalvik.vm.isa.arm.features u:object_r:exported_dalvik_prop:s0 exact string
dalvik.vm.isa.arm.variant u:object_r:exported_dalvik_prop:s0 exact string
dalvik.vm.isa.arm64.features u:object_r:exported_dalvik_prop:s0 exact string
dalvik.vm.isa.arm64.variant u:object_r:exported_dalvik_prop:s0 exact string
dalvik.vm.isa.mips.features u:object_r:exported_dalvik_prop:s0 exact string
dalvik.vm.isa.mips.variant u:object_r:exported_dalvik_prop:s0 exact string
dalvik.vm.isa.mips64.features u:object_r:exported_dalvik_prop:s0 exact string
dalvik.vm.isa.mips64.variant u:object_r:exported_dalvik_prop:s0 exact string
dalvik.vm.isa.unknown.features u:object_r:exported_dalvik_prop:s0 exact string
dalvik.vm.isa.unknown.variant u:object_r:exported_dalvik_prop:s0 exact string
dalvik.vm.isa.x86.features u:object_r:exported_dalvik_prop:s0 exact string
dalvik.vm.isa.x86.variant u:object_r:exported_dalvik_prop:s0 exact string
dalvik.vm.isa.x86_64.features u:object_r:exported_dalvik_prop:s0 exact string
dalvik.vm.isa.x86_64.variant u:object_r:exported_dalvik_prop:s0 exact string
dalvik.vm.jitinitialsize u:object_r:exported_dalvik_prop:s0 exact string
dalvik.vm.jitmaxsize u:object_r:exported_dalvik_prop:s0 exact string
dalvik.vm.jitprithreadweight u:object_r:exported_dalvik_prop:s0 exact int
dalvik.vm.jitthreshold u:object_r:exported_dalvik_prop:s0 exact int
dalvik.vm.jittransitionweight u:object_r:exported_dalvik_prop:s0 exact int
dalvik.vm.jniopts u:object_r:exported_dalvik_prop:s0 exact string
dalvik.vm.lockprof.threshold u:object_r:exported_dalvik_prop:s0 exact int
dalvik.vm.method-trace u:object_r:exported_dalvik_prop:s0 exact bool
dalvik.vm.method-trace-file u:object_r:exported_dalvik_prop:s0 exact string
dalvik.vm.method-trace-file-siz u:object_r:exported_dalvik_prop:s0 exact int
dalvik.vm.method-trace-stream u:object_r:exported_dalvik_prop:s0 exact bool
dalvik.vm.profilesystemserver u:object_r:exported_dalvik_prop:s0 exact bool
dalvik.vm.profilebootimage u:object_r:exported_dalvik_prop:s0 exact bool
dalvik.vm.stack-trace-dir u:object_r:exported_dalvik_prop:s0 exact string
dalvik.vm.usejit u:object_r:exported_dalvik_prop:s0 exact bool
dalvik.vm.usejitprofiles u:object_r:exported_dalvik_prop:s0 exact bool
dalvik.vm.zygote.max-boot-retry u:object_r:exported_dalvik_prop:s0 exact int
drm.service.enabled u:object_r:exported3_default_prop:s0 exact bool
keyguard.no_require_sim u:object_r:exported3_default_prop:s0 exact bool
media.recorder.show_manufacturer_and_model u:object_r:exported3_default_prop:s0 exact bool
media.stagefright.cache-params u:object_r:exported3_default_prop:s0 exact string
persist.bluetooth.a2dp_offload.cap u:object_r:bluetooth_a2dp_offload_prop:s0 exact string
persist.bluetooth.a2dp_offload.disabled u:object_r:bluetooth_a2dp_offload_prop:s0 exact bool
persist.config.calibration_fac u:object_r:exported3_default_prop:s0 exact string
persist.dbg.volte_avail_ovr u:object_r:exported3_default_prop:s0 exact int
persist.dbg.vt_avail_ovr u:object_r:exported3_default_prop:s0 exact int
persist.dbg.wfc_avail_ovr u:object_r:exported3_default_prop:s0 exact int
persist.radio.multisim.config u:object_r:exported3_radio_prop:s0 exact string
persist.sys.dalvik.vm.lib.2 u:object_r:exported2_system_prop:s0 exact string
persist.sys.media.avsync u:object_r:exported2_system_prop:s0 exact bool
persist.sys.hdmi.keep_awake u:object_r:exported2_system_prop:s0 exact bool
persist.sys.sf.color_saturation u:object_r:exported2_system_prop:s0 exact string
persist.sys.sf.native_mode u:object_r:exported2_system_prop:s0 exact int
pm.dexopt.ab-ota u:object_r:exported_pm_prop:s0 exact string
pm.dexopt.bg-dexopt u:object_r:exported_pm_prop:s0 exact string
pm.dexopt.boot u:object_r:exported_pm_prop:s0 exact string
pm.dexopt.first-boot u:object_r:exported_pm_prop:s0 exact string
pm.dexopt.install u:object_r:exported_pm_prop:s0 exact string
ro.audio.monitorRotation u:object_r:exported3_default_prop:s0 exact bool
ro.bluetooth.a2dp_offload.supported u:object_r:bluetooth_a2dp_offload_prop:s0 exact bool
ro.boot.vendor.overlay.theme u:object_r:exported_overlay_prop:s0 exact string
ro.boot.wificountrycode u:object_r:exported3_default_prop:s0 exact string
ro.bt.bdaddr_path u:object_r:exported_bluetooth_prop:s0 exact string
ro.camera.notify_nfc u:object_r:exported3_default_prop:s0 exact int
ro.com.android.dataroaming u:object_r:exported3_default_prop:s0 exact bool
ro.com.android.prov_mobiledata u:object_r:exported3_default_prop:s0 exact bool
ro.com.google.clientidbase u:object_r:exported3_default_prop:s0 exact string
ro.config.alarm_alert u:object_r:exported2_config_prop:s0 exact string
ro.config.media_vol_steps u:object_r:exported2_config_prop:s0 exact int
ro.config.notification_sound u:object_r:exported2_config_prop:s0 exact string
ro.config.ringtone u:object_r:exported2_config_prop:s0 exact string
ro.control_privapp_permissions u:object_r:exported3_default_prop:s0 exact string
ro.cp_system_other_odex u:object_r:exported3_default_prop:s0 exact int
ro.crypto.scrypt_params u:object_r:exported2_vold_prop:s0 exact string
ro.dalvik.vm.native.bridge u:object_r:exported_dalvik_prop:s0 exact string
ro.enable_boot_charger_mode u:object_r:exported3_default_prop:s0 exact bool
ro.gfx.driver.0 u:object_r:exported3_default_prop:s0 exact string
ro.hdmi.device_type u:object_r:exported3_default_prop:s0 exact string
ro.hdmi.wake_on_hotplug u:object_r:exported3_default_prop:s0 exact bool
ro.oem_unlock_supported u:object_r:exported3_default_prop:s0 exact int
ro.opengles.version u:object_r:exported3_default_prop:s0 exact int
ro.radio.noril u:object_r:exported3_default_prop:s0 exact string
ro.retaildemo.video_path u:object_r:exported3_default_prop:s0 exact string
ro.sf.disable_triple_buffer u:object_r:exported3_default_prop:s0 exact bool
ro.sf.lcd_density u:object_r:exported3_default_prop:s0 exact int
ro.storage_manager.enabled u:object_r:exported3_default_prop:s0 exact bool
ro.telephony.call_ring.multiple u:object_r:exported3_default_prop:s0 exact bool
ro.telephony.default_cdma_sub u:object_r:exported3_default_prop:s0 exact int
ro.telephony.default_network u:object_r:exported3_default_prop:s0 exact int
ro.url.legal u:object_r:exported3_default_prop:s0 exact string
ro.url.legal.android_privacy u:object_r:exported3_default_prop:s0 exact string
ro.vendor.build.security_patch u:object_r:vendor_security_patch_level_prop:s0 exact string
ro.zygote u:object_r:exported3_default_prop:s0 exact string
sendbug.preferred.domain u:object_r:exported3_default_prop:s0 exact string
sys.usb.controller u:object_r:exported2_system_prop:s0 exact string
sys.usb.ffs.max_read u:object_r:exported_ffs_prop:s0 exact int
sys.usb.ffs.max_write u:object_r:exported_ffs_prop:s0 exact int
sys.usb.mtp.device_type u:object_r:exported2_system_prop:s0 exact int
sys.usb.state u:object_r:exported2_system_prop:s0 exact string
telephony.lteOnCdmaDevice u:object_r:exported3_default_prop:s0 exact int
tombstoned.max_tombstone_count u:object_r:exported3_default_prop:s0 exact int
vold.post_fs_data_done u:object_r:exported2_vold_prop:s0 exact int
wlan.driver.status u:object_r:exported_wifi_prop:s0 exact enum ok unloaded

# vendor-init-readable|vendor-init-actionable
dev.bootcomplete u:object_r:exported3_system_prop:s0 exact bool
persist.sys.usb.usbradio.config u:object_r:exported3_system_prop:s0 exact string
sys.boot_completed u:object_r:exported3_system_prop:s0 exact bool
sys.retaildemo.enabled u:object_r:exported3_system_prop:s0 exact int

# vendor-init-settable|vendor-init-actionable
persist.sys.zram_enabled u:object_r:exported2_system_prop:s0 exact bool
sys.usb.config u:object_r:exported_system_radio_prop:s0 exact string
sys.usb.configfs u:object_r:exported_system_radio_prop:s0 exact int

# public-readable
aac_drc_boost u:object_r:exported2_default_prop:s0 exact int
aac_drc_cut u:object_r:exported2_default_prop:s0 exact int
aac_drc_enc_target_level u:object_r:exported2_default_prop:s0 exact int
aac_drc_heavy u:object_r:exported2_default_prop:s0 exact int
aac_drc_reference_level u:object_r:exported2_default_prop:s0 exact int
ro.aac_drc_effect_type u:object_r:exported2_default_prop:s0 exact int
drm.64bit.enabled u:object_r:exported2_default_prop:s0 exact bool
dumpstate.dry_run u:object_r:exported_dumpstate_prop:s0 exact bool
hal.instrumentation.enable u:object_r:exported2_default_prop:s0 exact bool
init.svc.tombstoned u:object_r:exported2_default_prop:s0 exact string
libc.debug.malloc.options u:object_r:exported2_default_prop:s0 exact string
libc.debug.malloc.program u:object_r:exported2_default_prop:s0 exact string
libc.debug.hooks.enable u:object_r:exported2_default_prop:s0 exact string
persist.sys.timezone u:object_r:exported_system_prop:s0 exact string
ro.adb.secure u:object_r:exported_secure_prop:s0 exact int
ro.arch u:object_r:exported2_default_prop:s0 exact string
ro.audio.ignore_effects u:object_r:exported2_default_prop:s0 exact bool
ro.baseband u:object_r:exported2_default_prop:s0 exact string
ro.boot.avb_version u:object_r:exported2_default_prop:s0 exact string
ro.boot.baseband u:object_r:exported2_default_prop:s0 exact string
ro.boot.bootdevice u:object_r:exported2_default_prop:s0 exact string
ro.boot.bootloader u:object_r:exported2_default_prop:s0 exact string
ro.boot.boottime u:object_r:exported2_default_prop:s0 exact string
ro.boot.console u:object_r:exported2_default_prop:s0 exact string
ro.boot.hardware u:object_r:exported2_default_prop:s0 exact string
ro.boot.hardware.color u:object_r:exported2_default_prop:s0 exact string
ro.boot.hardware.sku u:object_r:exported2_default_prop:s0 exact string
ro.boot.keymaster u:object_r:exported2_default_prop:s0 exact string
ro.boot.mode u:object_r:exported2_default_prop:s0 exact string
ro.boot.vbmeta.avb_version u:object_r:exported2_default_prop:s0 exact string
ro.boot.verifiedbootstate u:object_r:exported2_default_prop:s0 exact string
ro.boot.veritymode u:object_r:exported2_default_prop:s0 exact string
ro.bootimage.build.date u:object_r:exported2_default_prop:s0 exact string
ro.bootimage.build.date.utc u:object_r:exported2_default_prop:s0 exact int
ro.bootimage.build.fingerprint u:object_r:exported2_default_prop:s0 exact string
ro.bootloader u:object_r:exported2_default_prop:s0 exact string
ro.build.date u:object_r:exported2_default_prop:s0 exact string
ro.build.date.utc u:object_r:exported2_default_prop:s0 exact int
ro.build.description u:object_r:exported2_default_prop:s0 exact string
ro.build.display.id u:object_r:exported2_default_prop:s0 exact string
ro.build.fingerprint u:object_r:exported_fingerprint_prop:s0 exact string
ro.build.host u:object_r:exported2_default_prop:s0 exact string
ro.build.id u:object_r:exported2_default_prop:s0 exact string
ro.build.product u:object_r:exported2_default_prop:s0 exact string
ro.build.system_root_image u:object_r:exported2_default_prop:s0 exact bool
ro.build.tags u:object_r:exported2_default_prop:s0 exact string
ro.build.user u:object_r:exported2_default_prop:s0 exact string
ro.build.version.base_os u:object_r:exported2_default_prop:s0 exact string
ro.build.version.codename u:object_r:exported2_default_prop:s0 exact string
ro.build.version.incremental u:object_r:exported2_default_prop:s0 exact string
ro.build.version.preview_sdk u:object_r:exported2_default_prop:s0 exact int
ro.build.version.release u:object_r:exported2_default_prop:s0 exact string
ro.build.version.sdk u:object_r:exported2_default_prop:s0 exact int
ro.build.version.security_patch u:object_r:exported2_default_prop:s0 exact string
ro.crypto.state u:object_r:exported_vold_prop:s0 exact string
ro.crypto.type u:object_r:exported_vold_prop:s0 exact string
ro.debuggable u:object_r:exported2_default_prop:s0 exact int
ro.hardware u:object_r:exported2_default_prop:s0 exact string
ro.product.brand u:object_r:exported2_default_prop:s0 exact string
ro.product.cpu.abi u:object_r:exported2_default_prop:s0 exact string
ro.product.cpu.abilist u:object_r:exported2_default_prop:s0 exact string
ro.product.device u:object_r:exported2_default_prop:s0 exact string
ro.product.manufacturer u:object_r:exported2_default_prop:s0 exact string
ro.product.model u:object_r:exported2_default_prop:s0 exact string
ro.product.name u:object_r:exported2_default_prop:s0 exact string
ro.property_service.version u:object_r:exported2_default_prop:s0 exact int
ro.revision u:object_r:exported2_default_prop:s0 exact string
ro.secure u:object_r:exported_secure_prop:s0 exact int
service.bootanim.exit u:object_r:exported_system_prop:s0 exact int
sys.boot_from_charger_mode u:object_r:exported_system_prop:s0 exact int
vold.decrypt u:object_r:exported_vold_prop:s0 exact string

# vendor-init-settable|public-readable
aaudio.hw_burst_min_usec u:object_r:exported_default_prop:s0 exact int
aaudio.minimum_sleep_usec u:object_r:exported_default_prop:s0 exact int
aaudio.mixer_bursts u:object_r:exported_default_prop:s0 exact int
aaudio.mmap_exclusive_policy u:object_r:exported_default_prop:s0 exact int
aaudio.mmap_policy u:object_r:exported_default_prop:s0 exact int
aaudio.wakeup_delay_usec u:object_r:exported_default_prop:s0 exact int
gsm.sim.operator.numeric u:object_r:exported_radio_prop:s0 exact string
media.mediadrmservice.enable u:object_r:exported_default_prop:s0 exact bool
persist.rcs.supported u:object_r:exported_default_prop:s0 exact int
rcs.publish.status u:object_r:exported_radio_prop:s0 exact string
ro.board.platform u:object_r:exported_default_prop:s0 exact string
ro.boot.fake_battery u:object_r:exported_default_prop:s0 exact int
ro.boot.hardware.revision u:object_r:exported_default_prop:s0 exact string
ro.boot.product.hardware.sku u:object_r:exported_default_prop:s0 exact string
ro.boot.slot_suffix u:object_r:exported_default_prop:s0 exact string
ro.carrier u:object_r:exported_default_prop:s0 exact string
ro.config.low_ram u:object_r:exported_config_prop:s0 exact bool
ro.config.vc_call_vol_steps u:object_r:exported_config_prop:s0 exact int
ro.frp.pst u:object_r:exported_default_prop:s0 exact string
ro.hardware.activity_recognition u:object_r:exported_default_prop:s0 exact string
ro.hardware.audio u:object_r:exported_default_prop:s0 exact string
ro.hardware.audio.a2dp u:object_r:exported_default_prop:s0 exact string
ro.hardware.audio.hearing_aid u:object_r:exported_default_prop:s0 exact string
ro.hardware.audio.primary u:object_r:exported_default_prop:s0 exact string
ro.hardware.audio.usb u:object_r:exported_default_prop:s0 exact string
ro.hardware.audio_policy u:object_r:exported_default_prop:s0 exact string
ro.hardware.bootctrl u:object_r:exported_default_prop:s0 exact string
ro.hardware.camera u:object_r:exported_default_prop:s0 exact string
ro.hardware.consumerir u:object_r:exported_default_prop:s0 exact string
ro.hardware.context_hub u:object_r:exported_default_prop:s0 exact string
ro.hardware.egl u:object_r:exported_default_prop:s0 exact string
ro.hardware.fingerprint u:object_r:exported_default_prop:s0 exact string
ro.hardware.flp u:object_r:exported_default_prop:s0 exact string
ro.hardware.gatekeeper u:object_r:exported_default_prop:s0 exact string
ro.hardware.gps u:object_r:exported_default_prop:s0 exact string
ro.hardware.gralloc u:object_r:exported_default_prop:s0 exact string
ro.hardware.hdmi_cec u:object_r:exported_default_prop:s0 exact string
ro.hardware.hwcomposer u:object_r:exported_default_prop:s0 exact string
ro.hardware.input u:object_r:exported_default_prop:s0 exact string
ro.hardware.keystore u:object_r:exported_default_prop:s0 exact string
ro.hardware.keystore_desede u:object_r:exported_default_prop:s0 exact string
ro.hardware.lights u:object_r:exported_default_prop:s0 exact string
ro.hardware.local_time u:object_r:exported_default_prop:s0 exact string
ro.hardware.memtrack u:object_r:exported_default_prop:s0 exact string
ro.hardware.nfc u:object_r:exported_default_prop:s0 exact string
ro.hardware.nfc_nci u:object_r:exported_default_prop:s0 exact string
ro.hardware.nfc_tag u:object_r:exported_default_prop:s0 exact string
ro.hardware.nvram u:object_r:exported_default_prop:s0 exact string
ro.hardware.power u:object_r:exported_default_prop:s0 exact string
ro.hardware.radio u:object_r:exported_default_prop:s0 exact string
ro.hardware.sensors u:object_r:exported_default_prop:s0 exact string
ro.hardware.sound_trigger u:object_r:exported_default_prop:s0 exact string
ro.hardware.thermal u:object_r:exported_default_prop:s0 exact string
ro.hardware.tv_input u:object_r:exported_default_prop:s0 exact string
ro.hardware.type u:object_r:exported_default_prop:s0 exact string
ro.hardware.vehicle u:object_r:exported_default_prop:s0 exact string
ro.hardware.vibrator u:object_r:exported_default_prop:s0 exact string
ro.hardware.virtual_device u:object_r:exported_default_prop:s0 exact string
ro.hardware.vulkan u:object_r:exported_default_prop:s0 exact string
ro.kernel.qemu u:object_r:exported_default_prop:s0 exact int
ro.kernel.qemu. u:object_r:exported_default_prop:s0
ro.kernel.android.bootanim u:object_r:exported_default_prop:s0 exact int
ro.odm.build.date u:object_r:exported_default_prop:s0 exact string
ro.odm.build.date.utc u:object_r:exported_default_prop:s0 exact int
ro.odm.build.fingerprint u:object_r:exported_default_prop:s0 exact string
ro.oem.key1 u:object_r:exported_default_prop:s0 exact string
ro.product.board u:object_r:exported_default_prop:s0 exact string
ro.product.cpu.abilist32 u:object_r:exported_default_prop:s0 exact string
ro.product.cpu.abilist64 u:object_r:exported_default_prop:s0 exact string
ro.product.first_api_level u:object_r:exported_default_prop:s0 exact int
ro.product.odm.brand u:object_r:exported_default_prop:s0 exact string
ro.product.odm.device u:object_r:exported_default_prop:s0 exact string
ro.product.odm.manufacturer u:object_r:exported_default_prop:s0 exact string
ro.product.odm.model u:object_r:exported_default_prop:s0 exact string
ro.product.odm.name u:object_r:exported_default_prop:s0 exact string
ro.product.vendor.brand u:object_r:exported_default_prop:s0 exact string
ro.product.vendor.device u:object_r:exported_default_prop:s0 exact string
ro.product.vendor.manufacturer u:object_r:exported_default_prop:s0 exact string
ro.product.vendor.model u:object_r:exported_default_prop:s0 exact string
ro.product.vendor.name u:object_r:exported_default_prop:s0 exact string
ro.vendor.build.date u:object_r:exported_default_prop:s0 exact string
ro.vendor.build.date.utc u:object_r:exported_default_prop:s0 exact int
ro.vendor.build.fingerprint u:object_r:exported_default_prop:s0 exact string
ro.vndk.lite u:object_r:exported_default_prop:s0 exact bool
ro.vndk.version u:object_r:exported_default_prop:s0 exact string
ro.vts.coverage u:object_r:exported_default_prop:s0 exact int
wifi.direct.interface u:object_r:exported_default_prop:s0 exact string
wifi.interface u:object_r:exported_default_prop:s0 exact string

# vendor-init-actionable|public-readable
ro.boot.revision u:object_r:exported2_default_prop:s0 exact string
ro.bootmode u:object_r:exported2_default_prop:s0 exact string
ro.build.type u:object_r:exported2_default_prop:s0 exact string
sys.shutdown.requested u:object_r:exported_system_prop:s0 exact string
ubootenv.var.upgrade u:object_r:exported_system_prop:s0 exact string
