#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
晶晨机顶盒固件逆向分析助手
专门用于分析机顶盒固件系统卡刷包的签名机制

Author: By.举个🌰
Date: 2025
Copyright: By.举个🌰
"""

import os
import sys
import json
import hashlib
import zipfile
import subprocess
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import xml.etree.ElementTree as ET

class FirmwareReverseAssistant:
    """晶晨机顶盒固件逆向分析助手"""
    
    def __init__(self, work_dir: str = "."):
        self.work_dir = Path(work_dir)
        self.update_zip = self.work_dir / "update.zip"
        self.system_dir = self.work_dir / "system"
        self.analysis_results = {}
        
    def analyze_device_info(self) -> Dict:
        """分析设备基本信息"""
        print("🔍 分析设备基本信息...")
        
        build_prop_path = self.system_dir / "system" / "build.prop"
        device_info = {}
        
        if build_prop_path.exists():
            with open(build_prop_path, 'r', encoding='utf-8', errors='ignore') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        device_info[key] = value
        
        # 提取关键信息
        key_info = {
            "设备型号": device_info.get("ro.product.model", "未知"),
            "制造商": device_info.get("ro.product.manufacturer", "未知"),
            "Android版本": device_info.get("ro.build.version.release", "未知"),
            "API级别": device_info.get("ro.build.version.sdk", "未知"),
            "构建类型": device_info.get("ro.build.type", "未知"),
            "构建标签": device_info.get("ro.build.tags", "未知"),
            "芯片平台": device_info.get("ro.product.device", "未知"),
            "构建指纹": device_info.get("ro.build.fingerprint", "未知")
        }
        
        self.analysis_results["device_info"] = key_info
        return key_info
    
    def analyze_signature_mechanism(self) -> Dict:
        """分析签名验证机制"""
        print("🔐 分析签名验证机制...")
        
        signature_info = {
            "OTA证书": [],
            "公钥信息": [],
            "签名应用": [],
            "安全配置": {}
        }
        
        # 分析OTA证书
        otacerts_path = self.system_dir / "system" / "etc" / "security" / "otacerts.zip"
        if otacerts_path.exists():
            try:
                with zipfile.ZipFile(otacerts_path, 'r') as zip_ref:
                    cert_files = zip_ref.namelist()
                    signature_info["OTA证书"] = cert_files
                    
                    # 提取证书内容
                    for cert_file in cert_files:
                        cert_content = zip_ref.read(cert_file).decode('utf-8', errors='ignore')
                        signature_info[f"证书内容_{cert_file}"] = cert_content[:500] + "..."
            except Exception as e:
                signature_info["OTA证书错误"] = str(e)
        
        # 分析公钥文件
        pub_keys_path = self.system_dir / "system" / "etc" / "security" / "pub_keys.txt"
        if pub_keys_path.exists():
            with open(pub_keys_path, 'r', encoding='utf-8', errors='ignore') as f:
                pub_keys = f.read().strip().split('\n')
                signature_info["公钥信息"] = [key for key in pub_keys if key.strip()]
        
        # 分析签名应用配置
        sig_apps_path = self.system_dir / "system" / "etc" / "signatureApplications.xml"
        if sig_apps_path.exists():
            try:
                tree = ET.parse(sig_apps_path)
                root = tree.getroot()
                apps = []
                for key in root.findall('.//key'):
                    for pkg in key.findall('packageName'):
                        if pkg.text:
                            apps.append(pkg.text)
                signature_info["签名应用"] = apps
            except Exception as e:
                signature_info["签名应用错误"] = str(e)
        
        self.analysis_results["signature_mechanism"] = signature_info
        return signature_info
    
    def analyze_system_structure(self) -> Dict:
        """分析系统结构"""
        print("📁 分析系统结构...")
        
        structure_info = {
            "关键目录": [],
            "应用统计": {},
            "库文件统计": {},
            "配置文件": []
        }
        
        system_path = self.system_dir / "system"
        if system_path.exists():
            # 统计关键目录
            key_dirs = ["app", "priv-app", "framework", "lib", "bin", "etc"]
            for dir_name in key_dirs:
                dir_path = system_path / dir_name
                if dir_path.exists():
                    count = len(list(dir_path.iterdir()))
                    structure_info["关键目录"].append(f"{dir_name}: {count}个文件/目录")
            
            # 统计应用
            app_dirs = ["app", "priv-app"]
            total_apps = 0
            for app_dir in app_dirs:
                app_path = system_path / app_dir
                if app_path.exists():
                    apps = list(app_path.iterdir())
                    structure_info["应用统计"][app_dir] = len(apps)
                    total_apps += len(apps)
            
            structure_info["应用统计"]["总计"] = total_apps
            
            # 统计库文件
            lib_path = system_path / "lib"
            if lib_path.exists():
                so_files = list(lib_path.glob("*.so"))
                structure_info["库文件统计"]["共享库数量"] = len(so_files)
        
        self.analysis_results["system_structure"] = structure_info
        return structure_info
    
    def extract_certificates(self) -> List[str]:
        """提取证书信息"""
        print("📜 提取证书信息...")
        
        certificates = []
        
        # 提取OTA证书
        otacerts_path = self.system_dir / "system" / "etc" / "security" / "otacerts.zip"
        if otacerts_path.exists():
            extract_dir = self.work_dir / "otacerts_extracted"
            extract_dir.mkdir(exist_ok=True)
            
            try:
                with zipfile.ZipFile(otacerts_path, 'r') as zip_ref:
                    zip_ref.extractall(extract_dir)
                    
                for cert_file in extract_dir.glob("*.pem"):
                    certificates.append(str(cert_file))
                    
            except Exception as e:
                print(f"❌ 证书提取失败: {e}")
        
        return certificates
    
    def generate_analysis_report(self) -> str:
        """生成分析报告"""
        print("📊 生成分析报告...")
        
        report = []
        report.append("=" * 80)
        report.append("晶晨机顶盒固件逆向分析报告")
        report.append("By.举个🌰")
        report.append("=" * 80)
        report.append("")
        
        # 设备信息
        if "device_info" in self.analysis_results:
            report.append("📱 设备信息:")
            for key, value in self.analysis_results["device_info"].items():
                report.append(f"  {key}: {value}")
            report.append("")
        
        # 签名机制
        if "signature_mechanism" in self.analysis_results:
            report.append("🔐 签名验证机制:")
            sig_info = self.analysis_results["signature_mechanism"]
            
            if sig_info["OTA证书"]:
                report.append(f"  OTA证书文件: {', '.join(sig_info['OTA证书'])}")
            
            if sig_info["公钥信息"]:
                report.append(f"  公钥数量: {len(sig_info['公钥信息'])}")
                for i, key in enumerate(sig_info["公钥信息"][:2], 1):
                    report.append(f"    公钥{i}: {key[:64]}...")
            
            if sig_info["签名应用"]:
                report.append(f"  签名应用: {', '.join(sig_info['签名应用'])}")
            
            report.append("")
        
        # 系统结构
        if "system_structure" in self.analysis_results:
            report.append("📁 系统结构:")
            struct_info = self.analysis_results["system_structure"]
            
            for dir_info in struct_info["关键目录"]:
                report.append(f"  {dir_info}")
            
            if struct_info["应用统计"]:
                report.append(f"  应用总数: {struct_info['应用统计'].get('总计', 0)}")
            
            report.append("")
        
        report.append("=" * 80)
        report.append("分析完成")
        report.append("=" * 80)
        
        return "\n".join(report)
    
    def run_full_analysis(self):
        """运行完整分析"""
        print("🚀 开始晶晨机顶盒固件逆向分析...")
        print(f"📂 工作目录: {self.work_dir}")
        
        # 检查必要文件
        if not self.system_dir.exists():
            print("❌ system目录不存在，请确保已解压固件")
            return
        
        # 执行各项分析
        self.analyze_device_info()
        self.analyze_signature_mechanism()
        self.analyze_system_structure()
        self.extract_certificates()
        
        # 生成报告
        report = self.generate_analysis_report()
        
        # 保存报告
        report_file = self.work_dir / "firmware_analysis_report.txt"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        
        print(f"📄 分析报告已保存到: {report_file}")
        print("\n" + report)
        
        # 保存JSON格式的详细结果
        json_file = self.work_dir / "analysis_results.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(self.analysis_results, f, ensure_ascii=False, indent=2)
        
        print(f"📄 详细结果已保存到: {json_file}")

def main():
    """主函数"""
    print("🔧 晶晨机顶盒固件逆向分析助手")
    print("Copyright: By.举个🌰")
    print("-" * 50)
    
    # 创建分析器实例
    analyzer = FirmwareReverseAssistant()
    
    # 运行完整分析
    analyzer.run_full_analysis()

if __name__ == "__main__":
    main()
