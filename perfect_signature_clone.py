#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完美签名克隆工具
完全复制原始update.zip的签名结构到update_diy.zip

Author: By.举个🌰
Date: 2025
Copyright: By.举个🌰
"""

import os
import sys
import hashlib
import binascii
import zipfile
import shutil
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import tempfile
import datetime

class PerfectSignatureClone:
    """完美签名克隆工具"""
    
    def __init__(self):
        self.work_dir = Path(".")
        self.original_zip = "update.zip"
        self.target_zip = "update_diy.zip"
        
    def log(self, message: str):
        """输出日志"""
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        print(f"[{timestamp}] {message}")
    
    def extract_complete_signature_structure(self) -> Optional[Dict]:
        """提取完整的签名结构"""
        self.log("🔍 提取完整签名结构...")
        
        original_path = Path(self.original_zip)
        if not original_path.exists():
            self.log(f"❌ 找不到原始文件: {self.original_zip}")
            return None
        
        signature_structure = {
            'meta_inf_files': {},
            'manifest_content': None,
            'cert_sf_content': None
        }
        
        try:
            with zipfile.ZipFile(original_path, 'r') as zip_ref:
                file_list = zip_ref.namelist()
                
                # 提取所有META-INF文件
                for file_name in file_list:
                    if file_name.startswith("META-INF/"):
                        file_data = zip_ref.read(file_name)
                        signature_structure['meta_inf_files'][file_name] = file_data
                        
                        if file_name == "META-INF/MANIFEST.MF":
                            signature_structure['manifest_content'] = file_data.decode('utf-8')
                            self.log(f"✅ 提取 {file_name} ({len(file_data)} 字节)")
                        elif file_name == "META-INF/CERT.SF":
                            signature_structure['cert_sf_content'] = file_data.decode('utf-8')
                            self.log(f"✅ 提取 {file_name} ({len(file_data)} 字节)")
                        else:
                            self.log(f"✅ 提取 {file_name} ({len(file_data)} 字节)")
                
                if not signature_structure['meta_inf_files']:
                    self.log("❌ 原始文件没有META-INF结构")
                    return None
                
                self.log(f"✅ 提取了 {len(signature_structure['meta_inf_files'])} 个签名文件")
                return signature_structure
                
        except Exception as e:
            self.log(f"❌ 提取签名结构失败: {str(e)}")
            return None
    
    def analyze_manifest_structure(self, manifest_content: str) -> Dict:
        """分析MANIFEST.MF的结构"""
        self.log("📋 分析MANIFEST.MF结构...")
        
        lines = manifest_content.split('\n')
        structure = {
            'header': [],
            'file_entries': [],
            'current_entry': None
        }
        
        current_entry = {}
        in_header = True
        
        for line in lines:
            if in_header and (line.startswith('Name: ') or not line.strip()):
                if line.startswith('Name: '):
                    in_header = False
                    current_entry = {'name': line, 'attributes': []}
                else:
                    structure['header'].append(line)
            elif line.startswith('Name: '):
                if current_entry:
                    structure['file_entries'].append(current_entry)
                current_entry = {'name': line, 'attributes': []}
            elif line.strip() and current_entry is not None:
                current_entry['attributes'].append(line)
            elif not line.strip() and current_entry:
                structure['file_entries'].append(current_entry)
                current_entry = None
        
        if current_entry:
            structure['file_entries'].append(current_entry)
        
        self.log(f"✅ 分析完成: {len(structure['file_entries'])} 个文件条目")
        return structure
    
    def create_perfect_manifest(self, target_zip_path: Path, original_structure: Dict) -> str:
        """创建完美匹配的MANIFEST.MF"""
        self.log("📝 创建完美匹配的MANIFEST.MF...")
        
        # 使用原始的头部
        manifest_lines = original_structure['header'].copy()
        
        try:
            with zipfile.ZipFile(target_zip_path, 'r') as zip_ref:
                # 为目标文件中的每个文件创建条目
                for file_info in zip_ref.filelist:
                    # 跳过META-INF目录和目录条目
                    if file_info.filename.startswith("META-INF/") or file_info.filename.endswith("/"):
                        continue
                    
                    # 读取文件内容并计算哈希
                    file_data = zip_ref.read(file_info.filename)
                    
                    # 计算SHA-1哈希 (使用与原始相同的格式)
                    sha1_hash = hashlib.sha1(file_data).digest()
                    sha1_b64 = binascii.b2a_base64(sha1_hash).decode().strip()
                    
                    # 添加文件条目 (使用原始格式)
                    manifest_lines.extend([
                        f"Name: {file_info.filename}",
                        f"SHA1-Digest: {sha1_b64}",
                        ""
                    ])
            
            manifest_content = "\n".join(manifest_lines)
            self.log(f"✅ 完美MANIFEST.MF创建完成")
            return manifest_content
            
        except Exception as e:
            self.log(f"❌ 创建完美MANIFEST.MF失败: {str(e)}")
            return ""
    
    def create_perfect_cert_sf(self, manifest_content: str, original_cert_sf: str) -> str:
        """创建完美匹配的CERT.SF"""
        self.log("🔐 创建完美匹配的CERT.SF...")
        
        # 分析原始CERT.SF的头部结构
        original_lines = original_cert_sf.split('\n')
        cert_sf_lines = []
        
        # 复制头部信息直到第一个文件条目
        for line in original_lines:
            if line.startswith('Name: '):
                break
            cert_sf_lines.append(line)
        
        # 重新计算清单哈希
        manifest_bytes = manifest_content.encode('utf-8')
        manifest_sha1 = hashlib.sha1(manifest_bytes).digest()
        manifest_sha1_b64 = binascii.b2a_base64(manifest_sha1).decode().strip()
        
        # 更新清单哈希行
        for i, line in enumerate(cert_sf_lines):
            if line.startswith('SHA1-Digest-Manifest: '):
                cert_sf_lines[i] = f"SHA1-Digest-Manifest: {manifest_sha1_b64}"
                break
        
        # 为每个文件条目创建签名条目
        manifest_lines = manifest_content.split('\n')
        current_entry = []
        
        for line in manifest_lines:
            if line.startswith("Name: "):
                if current_entry:
                    # 处理前一个条目
                    entry_content = '\n'.join(current_entry) + '\n'
                    entry_sha1 = hashlib.sha1(entry_content.encode()).digest()
                    entry_sha1_b64 = binascii.b2a_base64(entry_sha1).decode().strip()
                    
                    cert_sf_lines.extend([
                        current_entry[0],  # Name: 行
                        f"SHA1-Digest: {entry_sha1_b64}",
                        ""
                    ])
                
                current_entry = [line]
            elif line.strip() and current_entry:
                current_entry.append(line)
            elif not line.strip() and current_entry:
                # 处理最后一个条目
                entry_content = '\n'.join(current_entry) + '\n'
                entry_sha1 = hashlib.sha1(entry_content.encode()).digest()
                entry_sha1_b64 = binascii.b2a_base64(entry_sha1).decode().strip()
                
                cert_sf_lines.extend([
                    current_entry[0],  # Name: 行
                    f"SHA1-Digest: {entry_sha1_b64}",
                    ""
                ])
                current_entry = []
        
        cert_sf_content = "\n".join(cert_sf_lines)
        self.log("✅ 完美CERT.SF创建完成")
        return cert_sf_content
    
    def apply_perfect_signature(self, target_zip_path: Path, signature_structure: Dict) -> bool:
        """应用完美签名"""
        self.log(f"✍️ 应用完美签名到 {target_zip_path.name}...")
        
        try:
            with tempfile.TemporaryDirectory() as temp_dir:
                temp_path = Path(temp_dir)
                temp_zip = temp_path / "temp.zip"
                
                # 复制目标文件
                shutil.copy2(target_zip_path, temp_zip)
                
                # 移除现有签名
                self.remove_existing_signature(temp_zip)
                
                # 分析原始MANIFEST结构
                original_manifest_structure = self.analyze_manifest_structure(
                    signature_structure['manifest_content']
                )
                
                # 创建完美匹配的签名文件
                manifest_content = self.create_perfect_manifest(temp_zip, original_manifest_structure)
                cert_sf_content = self.create_perfect_cert_sf(
                    manifest_content, signature_structure['cert_sf_content']
                )
                
                # 创建META-INF目录
                meta_inf_dir = temp_path / "META-INF"
                meta_inf_dir.mkdir(exist_ok=True)
                
                # 写入新的MANIFEST.MF和CERT.SF
                manifest_path = meta_inf_dir / "MANIFEST.MF"
                with open(manifest_path, 'w', encoding='utf-8') as f:
                    f.write(manifest_content)
                
                cert_sf_path = meta_inf_dir / "CERT.SF"
                with open(cert_sf_path, 'w', encoding='utf-8') as f:
                    f.write(cert_sf_content)
                
                # 复制所有其他META-INF文件
                for meta_file, meta_data in signature_structure['meta_inf_files'].items():
                    if meta_file not in ["META-INF/MANIFEST.MF", "META-INF/CERT.SF"]:
                        meta_path = meta_inf_dir / Path(meta_file).name
                        with open(meta_path, 'wb') as f:
                            f.write(meta_data)
                
                # 将所有签名文件添加到ZIP
                with zipfile.ZipFile(temp_zip, 'a') as zip_ref:
                    # 添加MANIFEST.MF和CERT.SF
                    zip_ref.write(manifest_path, "META-INF/MANIFEST.MF")
                    zip_ref.write(cert_sf_path, "META-INF/CERT.SF")
                    
                    # 添加其他META-INF文件
                    for meta_file in signature_structure['meta_inf_files'].keys():
                        if meta_file not in ["META-INF/MANIFEST.MF", "META-INF/CERT.SF"]:
                            meta_name = Path(meta_file).name
                            meta_path = meta_inf_dir / meta_name
                            if meta_path.exists():
                                zip_ref.write(meta_path, meta_file)
                
                # 替换原文件
                shutil.copy2(temp_zip, target_zip_path)
                
            self.log("✅ 完美签名应用完成")
            return True
            
        except Exception as e:
            self.log(f"❌ 应用完美签名失败: {str(e)}")
            return False
    
    def remove_existing_signature(self, zip_path: Path):
        """移除现有签名"""
        try:
            # 读取所有非签名文件
            files_to_keep = []
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                for file_info in zip_ref.filelist:
                    if not file_info.filename.startswith("META-INF/"):
                        files_to_keep.append((file_info.filename, zip_ref.read(file_info.filename)))
            
            # 重新创建ZIP文件
            with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zip_ref:
                for filename, data in files_to_keep:
                    zip_ref.writestr(filename, data)
                    
        except Exception as e:
            self.log(f"❌ 移除现有签名失败: {str(e)}")
            raise
    
    def perfect_clone_and_compare(self) -> bool:
        """执行完美克隆和比较"""
        self.log("🚀 开始完美签名克隆...")
        
        # 检查文件存在性
        if not Path(self.original_zip).exists():
            self.log(f"❌ 找不到原始文件: {self.original_zip}")
            return False
        
        if not Path(self.target_zip).exists():
            self.log(f"❌ 找不到目标文件: {self.target_zip}")
            return False
        
        # 1. 提取完整签名结构
        signature_structure = self.extract_complete_signature_structure()
        if not signature_structure:
            return False
        
        # 2. 创建完美克隆文件
        perfect_clone = f"perfect_signed_{self.target_zip}"
        perfect_path = Path(perfect_clone)
        
        # 复制目标文件
        shutil.copy2(self.target_zip, perfect_path)
        
        # 3. 应用完美签名
        if not self.apply_perfect_signature(perfect_path, signature_structure):
            return False
        
        # 4. 最终比较
        self.log("🔍 进行最终签名比较...")
        self.compare_final_signatures(Path(self.original_zip), perfect_path)
        
        self.log(f"\n✅ 完美签名克隆完成: {perfect_clone}")
        return True
    
    def compare_final_signatures(self, original_path: Path, cloned_path: Path):
        """最终签名比较"""
        print("\n" + "="*60)
        print("完美签名比较结果")
        print("="*60)
        
        try:
            original_meta = {}
            cloned_meta = {}
            
            # 提取原始文件的META-INF信息
            with zipfile.ZipFile(original_path, 'r') as zip_ref:
                for file_name in zip_ref.namelist():
                    if file_name.startswith("META-INF/"):
                        file_data = zip_ref.read(file_name)
                        original_meta[file_name] = {
                            'size': len(file_data),
                            'sha256': hashlib.sha256(file_data).hexdigest()
                        }
            
            # 提取克隆文件的META-INF信息
            with zipfile.ZipFile(cloned_path, 'r') as zip_ref:
                for file_name in zip_ref.namelist():
                    if file_name.startswith("META-INF/"):
                        file_data = zip_ref.read(file_name)
                        cloned_meta[file_name] = {
                            'size': len(file_data),
                            'sha256': hashlib.sha256(file_data).hexdigest()
                        }
            
            # 比较结果
            all_files = set(original_meta.keys()) | set(cloned_meta.keys())
            matching_files = 0
            
            print(f"\n📊 签名文件对比:")
            for file_name in sorted(all_files):
                if file_name in original_meta and file_name in cloned_meta:
                    orig_info = original_meta[file_name]
                    clone_info = cloned_meta[file_name]
                    
                    if orig_info['sha256'] == clone_info['sha256']:
                        print(f"  ✅ {file_name}: 完全匹配")
                        matching_files += 1
                    else:
                        print(f"  ❌ {file_name}: 不匹配 (大小: {orig_info['size']} vs {clone_info['size']})")
                elif file_name in original_meta:
                    print(f"  ⚠️ {file_name}: 仅在原始文件中")
                else:
                    print(f"  ⚠️ {file_name}: 仅在克隆文件中")
            
            match_percentage = (matching_files / len(all_files)) * 100 if all_files else 0
            print(f"\n📈 总体匹配度: {match_percentage:.1f}%")
            
            if match_percentage == 100:
                print("🎉 签名完美匹配！")
            elif match_percentage >= 80:
                print("✅ 签名高度匹配，应该可以正常使用")
            else:
                print("⚠️ 签名匹配度较低，可能需要进一步调整")
                
        except Exception as e:
            print(f"❌ 比较失败: {str(e)}")

def main():
    """主函数"""
    print("🔧 完美签名克隆工具")
    print("完全复制 update.zip 的签名结构")
    print("Copyright: By.举个🌰")
    print("-" * 50)
    
    # 创建完美克隆工具实例
    clone_tool = PerfectSignatureClone()
    
    # 执行完美克隆
    success = clone_tool.perfect_clone_and_compare()
    
    if success:
        print("\n🎉 完美签名克隆完成!")
        print("现在可以使用 perfect_signed_update_diy.zip 进行刷机")
        print("这个文件应该具有与原始 update.zip 完全相同的签名结构")
    else:
        print("\n❌ 完美签名克隆失败，请检查错误信息")

if __name__ == "__main__":
    main()
