
# begin build properties
# autogenerated by buildinfo.sh
ro.build.id=CM311-1s-NL
ro.build.display.id=CM311-1s-NL test-keys
ro.build.version.incremental=V.686.03
ro.build.version.sdk=28
ro.build.version.preview_sdk=0
ro.build.version.codename=REL
ro.build.version.all_codenames=REL
ro.build.version.release=9
ro.build.version.security_patch=2018-08-05
ro.build.version.base_os=
ro.build.version.min_supported_target_sdk=17
ro.build.date=Thu Jun 13 19:56:28 CST 2024
ro.build.date.utc=1718279788
ro.build.type=userdebug
ro.build.user=jenkins
ro.build.host=cmcc
ro.build.tags=test-keys
ro.build.flavor=p291_iptv-userdebug
ro.build.system_root_image=false
ro.product.model=CM311-1s-NL
ro.product.brand=CM311-1s-NL
ro.product.name=CM311-1s-NL
ro.product.device=p291_iptv
# ro.product.cpu.abi and ro.product.cpu.abi2 are obsolete,
# use ro.product.cpu.abilist instead.
ro.product.cpu.abi=armeabi-v7a
ro.product.cpu.abi2=armeabi
ro.product.cpu.abilist=armeabi-v7a,armeabi
ro.product.cpu.abilist32=armeabi-v7a,armeabi
ro.product.cpu.abilist64=
ro.product.manufacturer=CMDC
ro.product.manufactureroui=FF0001
ro.product.locale=zh-CN


ro.wifi.channels=
# ro.build.product is obsolete; use ro.product.device
ro.build.product=CM311-1s-NL
# Do not try to parse description, fingerprint, or thumbprint
ro.build.description=Amlogic905L3-eng 9.0.0 CM311-1s-NL V.686.03 test-keys
ro.build.fingerprint=AndroidP/CMDC/S905L3:9.0.0/CM311-1s-NL/V.686.03:eng/test-keys
ro.build.characteristics=device,mbx,nosdcard
ro.build.aml.baseline="AmlogicIPTVAndroidP_R210531_T20220709"
# end build properties
#
# from device/amlogic/p291_iptv/system_chinamobile.prop
#
#ro.product.firmware=00442001
#ro.product.otaupdateurl=http://***********:8080/otaupdate/update
#ro.adb.secure=1
#ro.vlan.enable=false
#rild.libpath=/system/lib/libreference-ril.so
#rild.libargs=-d /dev/ttyS0
#ro.sf.lcd_density=240
#keyguard.no_require_sim=1

#persist.sys.autosuspend.cec.enable=true
#persist.sys.mbx.timeout.enable=false
#persist.sys.autosuspend.cec.time=1000
#sys.screensaver.enable=false
#ro.statusbar.widget=false
#ro.statusbar.button=false
#ro.statusbar.yearmonthdayweek=false

#wifi.interface=ra0
# Time between scans in seconds. Keep it high to minimize battery drain.
# This only affects the case in which there are remembered access points,
# but none are in range.
#wifi.supplicant_scan_interval = 60
#alsa.mixer.playback.master=DAC2 Analog
#alsa.mixer.capture.master=Analog

#hwui.render_dirty_regions=false

#force GPU rendering
#persist.sys.ui.hw=true

#timeout control
http.getc.timeout_us = 10000000

# Configure features
#hw.nopm=true
#hw.nobattery=true
#hw.nophone=true
#hw.novibrate=true
#hw.nogps=true
#hw.cameras=0
#hw.hasethernet=true
#hw.hasusbcamera=true
#hw.has.accelerometer=true

#used in packages/apps/Settings/.../Utils.java
#hw.has.bluetooth=false
#ro.ethernet.default_on=true
#ro.config.low_ram=false
#dalvik.vm.jit.codecachesize=0

#ro.screen.has.timeout=false
#ro.screen.has.brightness=false
#ro.screen.has.tvout=true
#ro.platform.has.security=false
#ro.platform.has.tts=false
#ro.platform.has.touch=false
#ro.platform.has.mbxuimode=true
#ro.platform.has.1080scale=2
#ro.fontScale=1.3
#ro.platform.has.digitaudio=true
#ro.platform.has.defaulttvfreq=false
#ro.hw.ethernet.onboard=true
#ro.platform.has.cvbsmode=false
#ro.platform.hdmionly=true
#ro.platform.has.cecmode=false
#ro.platform.filter.modes=480i60hz,576i50hz,smpte24hz
#ro.platform.filter.modes=2160p50hz42010bit,2160p60hz42010bit,2160p50hz42212bit,2160p60hz42212bit
#sys.output.10bit=true

#ro.platform.has.systemlog=false

# Use OSD2 mouse patch
#ro.ui.cursor=osd2
#ro.ui.cursor.autohide=true
#ro.ui.cursor.timeout=10000

#ro.ui.cursor=surface

#set to 0 temporarily so touch works without other changes
#ro.sf.hwrotation=0

#ro.hardware=amlogic

#camera 1080p
#ro.camera.preview.MaxSize=1920x1088
#ro.camera.preview.LimitedRate=1920x1088x30,1280x720x30,640x480x30,320x240x28
#ro.camera.preview.UseMJPEG=1
#ro.camera.preview.UseHwMJPEG=1
#enable mjpeg multi-dec
media.omx.multi_mode=11

#hw.encoder.freerun=1
#hw.encoder.temp.enable=1
#hw.encoder.reencode.enable=0

# Enable 32-bit OSD
#sys.fb.bits=32

# Disable GPS
#gps.enable=false

#keep landscape in launcher
#sys.keeplauncher.landcape=true

#Enable player buildin
media.amsuperplayer.enable=true
media.amplayer.enable-acodecs=ac3,eac3,rm,dts,thd,aac,m4a,asf
media.amplayer.enable=true
media.amplayer.support-exname=divx,h264,avi,ts,m2ts,mkv,mp4,mpg,mpeg,rm,rmvb,wmv,ts,dat,vob,vc1
media.amplayer.support-exname1=mp2,mp3,ac3
media.amsuperplayer.m4aplayer=STAGEFRIGHT_PLAYER
media.amsuperplayer.defplayer=PV_PLAYER
media.amplayer.thumbnail=true
media.amplayer.thumbnail4http=true
media.amplayer.startmode=true
media.playstatus.enable=1

#FOR ffmpeg cache module
libplayer.cache.debug=1
libplayer.cache.enable=1
libplayer.cache.seekenable=1
libplayer.cache.keepframe_en=0
libplayer.cache.enterkeepms=5000
libplayer.cache.amaxframes=4000000
libplayer.cache.vmaxframes=2000000
libplayer.cache.smaxframes=2000000
libplayer.cache.maxmem=94371840
libplayer.cache.backseek=300
libplayer.cache.bigpktnum=4
media.player.seekX=0
media.libplayer.nobufferstart=0
media.amplayer.seek_async=0
media.amplayer.chmobilekpi=0
libplayer.tcp.get_dns_type=0

sys.imgplayer.freq_down=1
#FOR IPTV
libplayer.ffmpeg.lpbufsizemax=10485760
libplayer.switch.format = 1
media.amplayer.lpbufferlevel=0.002
media.amplayer.onbuffering.S=0.3
media.amplayer.buffertime=6
media.amplayer.lowlevel=0.001
media.amplayer.midlevel=0.07
media.amplayer.middlelevel.4k=0.1
media.amplayer.highlevel=0.95
media.amplayer.divtime=1
media.amplayer.force_buf_enable=1
media.amplayer.force_buf_thres=400
media.amadec.prefilltime=50
media.amplayer.force_buf_exit=0.008
media.amplayer.refmode=0
media.amplayer.dropmaxtime=10000
media.amplayer.pre_droppcm=0
#FOR IPTV,delay send buffering.
media.amplayer.delaybuffering.s=2
media.amplayer.delayprobebuffering.s=0
media.amplayer.delaybuffering=3
media.amplayer.smooth_region=7200000000
media.amplayer.bufing_timeout=90

#update state interval
#media.amplayer.update_interval=500
# deep diagnose
media.amplayer.hls_notify_en=1
media.hls.range_type=1
libplayer.tcp.timeout=1000
libplayer.tcp.dnscache=1
libplayer.hls.start_from_top=1

#update duration with url info
media.amplayer.dur_update=1
media.amplayer.lpbufferlevel=0.05
#media.p2pplay.enable=true
media.amplayer.widevineenable=false
media.amplayer.dsource4local=1
media.arm.audio.decoder=ape,flac,dts,ac3,eac3,wma,wmapro,mp3,aac,vorbis,raac,cook,amr,pcm,adpcm,aac_latm,rm
media.wfd.use-pcm-audio=true
media.wfd.videoreolutiontype=0
media.wfd.videoreolutiongroup=5
media.wfd.videoframerate=20
media.html5videowin.enable=1
media.decoder.vfm.defmap=decoder ppmgr deinterlace amvideo
media.amplayer.seekkeyframe=1
media.amplayer.dropwaitxms=2000
media.amplayer.seekmode=1
media.omx.LowLatency_mode=1
mbx.3D_Bright.enable=false
# Nand write need force sync when gadget
#gadget.nand.force_sync=true
#enable bluetooth when suspend
#bt.keep_on.enable=true
#bt.auto.enable=true

# Camera exif
#ro.camera.exif.make=M8b
#ro.camera.exif.model=m200
#hide status bar
#persist.sys.hideStatusBar=true

#support media poll uevent,can use sd cardread on usb port
#has.media.poll=true

#used forward seek for libplayer
media.libplayer.seek.fwdsearch=1
media.libplayer.net.frameseek=1


# Disable preload-class
#ro.amlogic.no.preloadclass=1

#virtualsd.enable=true

#service.adb.tcp.port=5555
#const.window.w=1280
#const.window.h=720
#sys.defaultStream.ismusic=true
#ro.app.optimization=true
#ro.platform.has.realoutputmode=true

#add livhls,libcurl as default hls
media.libplayer.curlenable=false
media.libplayer.modules=vhls_mod,dash_mod,curl_mod,prhls_mod,vm_mod,bluray_mod

#sofrware demux
libplayer.netts.recalcpts=1
libplayer.livets.softdemux=1
libplayer.ts.softdemux=1
libplayer.hls.stpos=0
#Time Zone
persist.sys.timezone=Asia/Shanghai
#ro.osd2.size=64x64

#new feature 81282
#ro.alarm.align=true

#for AmUMediaPlayer hls
#media.ammediaplayer.enable=1
hls.aml.enable=1
hls.curl.enable=0

#for AmUMediaPlayer Circle Buffer
iptv.enablebuf=0

#add for video boot, 1 means use video boot, others not .
#service.bootvideo=0
#service.bootadv=1

#service.bootvideo.checkexit=false
#ro.product.bootvideo.type=zip

#new feature 81297
#ro.permissions.settings=true

#don't need brightness
#prop.sp.brightness=off
#ro.quickboot.enable=false

#config.disable_telephony=true
#config.disable_bluetooth=true
#config.enable_quickboot=true
#config.disable_vibrator=true
#config.disable_location=true

#has spdif output
#ro.hdmi.spdif=true

#for China mobile
net.ppp.retrycount=2
persist.sys.autosuspend.hdmi=false
sys.deepdiagnose.support=1
sys.wifi.ipv6.enable=true
sys.broadcast.permit=true
persist.net.monitor=true

#for all cec key on or off
persist.sys.cec.enable.key=true

#for cec volume key on or off
persist.sys.cec.enable.volume.key=false

#for cec control enable or disable
persist.vendor.sys.cec.controlenabled=false

ro.iptv.mbox=true
ro.autocreate.download=true
ro.product.name=CM311-1s-NL
net.pppoe.running=1
net.ethwifi.coexist=true
ro.mac=
epg.login=
epg.userid=
epg.token=
epg.mobile.userid=
epg.mobile.token=
epg.mobile.deviceid=
ro.media.timeshift=0
sys.settings.support=1
sys.settings.support.net.flags=7
epg.eccode=
epg.copyrightid=
epg.indexurl=
epg.cmcchomeurl=
epg.authcode=
epg.eccoporationcode=
epg.speechchannel.bussy=
epg.usergroup=
epg.accountidentity=
#ro.product.type=0
#ro.devicetype=stb
net.dhcpc.ipver=
net.dhcpc.username=
net.dhcpc.pswd=
net.dhcpc.option=
sys.cmcc.hls.adaptrate=
sys.cmcc.hls.firstrate=
sys.cmcc.video.contentmode=
service.media.playstatus=stopped
#sys.settings.support.languages=zh-CN,zh-HK,en-US
persist.sys.language=ZH EN
persist.sys.country=CN,HK,US
sys.app.oom_adj=1
#sys.deflauncher.cls=com.chinamobile.startup.activity.StartUpMainActivity
sys.deflaunchersetting.pkg=com.shcmcc.setting
sys.settings.support.bluetooth=1
#ro.flash.size=NAND 4G
#ro.memory.size=

#for webkit retry
webkit.loadurl.retry_cnt=3
webkit.loadurl.timeout=10

sys.settings.support.ap.flags=0

sys.settings.support.spdif=0
ro.media.dolby=0
ro.media.maxresolution=0
sys.settings.support.net.flags=7
sys.deepdiagnose.support=1

#K8 popo issue
persist.service.ki.builtin=0

#for webkit retry
webkit.loadurl.retry_cnt=3
webkit.loadurl.timeout=10
#ethernet & wifi coexist
net.ethwifi.prior=ethernet

sys.proj.type=mobile


#instaboot bootanimation config
#onetime: bootanimation run onetime
#loop: bootanimation run in loop
#none: no bootanimation when instaboot restored
#ro.instaboot.bootanimation=onetime
#ro.instaboot.animation_time=5
#net.pppoe.padt_timeout=600

#for tr069
#ro.tr069.enable=true
#sys.supend.delaytime=5000
#sys.start.boot=true
#sys.broadcast.policy=sticky
#media.player.cmcc_report.enable=true
#sys.support.smpte=true

#force vd1 for 3/4 player instances
vendor.hwc.forceOneLayer=1
#for kernel to logcat
#ro.logd.kernel=1
#ro.logd.size=2m
#for amlchat app
amlchat.status.enable=disable
#for default volume
persist.audio.volume=8
persist.sys.boot.volume=8
sys.shcmcc.test.hideview=true

#enable ffmpeg ipv6 tcp
media.libplayer.ipv4only=0

#enable liveplayer mediahal videotunnel
vendor.media.liveplayer.enable-mediahal-videodec=1
vendor.media.liveplayer.enable-video-tunnel=1
#suppoort mpeg2 audio in liveplayer
vendor.media.liveplayer.mpeg2-use-ffmpeg-audio-decoder=1
#set probe size on AmIptvMedia
iptv.probesize=8388608
#support report blur softprobe event
media.player.cmcc_report.enable=1

persist.sys.mscanner=false

#
# ADDITIONAL_BUILD_PROPERTIES
#
ro.bionic.ld.warning=1
ro.art.hiddenapi.warning=1
ro.treble.enabled=true
persist.sys.dalvik.vm.lib.2=libart.so
dalvik.vm.isa.arm.variant=cortex-a9
dalvik.vm.isa.arm.features=default
dalvik.vm.lockprof.threshold=500
net.bt.name=Android
dalvik.vm.stack-trace-dir=/data/anr
ro.build.expect.bootloader=01.01.180822.145544
ro.expect.recovery_id=0xaec1b1337781fdfd854032e494c220f7ae718f1f000000000000000000000000
ro.build.devicemodel=CM311-1s-NL
ro.hardwareno=CM311-1s-NL
ro.build.equipment=CM311-1s-NL
ro.factory.name=CMDC
persist.sys.wifi.dualstack=10
persist.sys.app.silentInstaller=true
ro.standby.action=net.sunniwell.action.POWER_WINDOW_SERVICE
sys.standby.mode=1
media.filter.heaac=1
persist.sys.usb.config=none
ro.prop.heaac.filter=false
sys.platform.media=mlogic
sys.pop.ups=true
