#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
绕过签名验证工具
专门用于解决Error 21签名验证问题

Author: By.举个🌰
Date: 2025
Copyright: By.举个🌰
"""

import os
import sys
import hashlib
import binascii
import zipfile
import shutil
from pathlib import Path
import datetime

class SignatureBypassTool:
    """签名验证绕过工具"""
    
    def __init__(self):
        self.work_dir = Path(".")
        
    def log(self, message: str):
        """输出日志"""
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        print(f"[{timestamp}] {message}")
    
    def create_bypass_package(self, input_file: str) -> bool:
        """创建绕过验证的包"""
        input_path = Path(input_file)
        if not input_path.exists():
            self.log(f"❌ 文件不存在: {input_file}")
            return False
        
        # 创建绕过版本
        bypass_file = f"bypass_{input_path.name}"
        bypass_path = Path(bypass_file)
        
        self.log(f"🔄 创建绕过验证包: {bypass_file}")
        
        try:
            # 复制原文件
            shutil.copy2(input_path, bypass_path)
            
            # 移除所有签名相关文件
            self.remove_all_signatures(bypass_path)
            
            # 添加绕过标记
            self.add_bypass_marker(bypass_path)
            
            self.log(f"✅ 绕过验证包创建完成: {bypass_file}")
            return True
            
        except Exception as e:
            self.log(f"❌ 创建绕过包失败: {str(e)}")
            return False
    
    def remove_all_signatures(self, zip_path: Path):
        """移除所有签名文件"""
        self.log("🗑️ 移除所有签名文件...")
        
        try:
            # 读取非签名文件
            files_to_keep = []
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                for file_info in zip_ref.filelist:
                    # 保留所有非META-INF的文件
                    if not file_info.filename.startswith("META-INF/"):
                        files_to_keep.append((file_info.filename, zip_ref.read(file_info.filename)))
            
            # 重新创建ZIP文件
            with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED, compresslevel=6) as zip_ref:
                for filename, data in files_to_keep:
                    zip_ref.writestr(filename, data)
            
            self.log("✅ 签名文件已移除")
            
        except Exception as e:
            self.log(f"❌ 移除签名失败: {str(e)}")
            raise
    
    def add_bypass_marker(self, zip_path: Path):
        """添加绕过标记文件"""
        self.log("📝 添加绕过标记...")
        
        try:
            marker_content = f"""# 签名验证绕过标记
# By.举个🌰
# 创建时间: {datetime.datetime.now().isoformat()}
# 
# 此文件表明该包已移除签名验证
# 需要在设备上禁用签名验证或替换验证公钥
"""
            
            with zipfile.ZipFile(zip_path, 'a') as zip_ref:
                zip_ref.writestr("bypass_signature_check.txt", marker_content)
            
            self.log("✅ 绕过标记已添加")
            
        except Exception as e:
            self.log(f"❌ 添加标记失败: {str(e)}")
    
    def generate_replacement_keys(self):
        """生成替换用的公钥"""
        self.log("🔑 生成替换公钥...")
        
        try:
            # 读取我们之前生成的公钥
            our_pubkey_file = Path("ota_public_key.txt")
            if our_pubkey_file.exists():
                with open(our_pubkey_file, 'r') as f:
                    our_pubkey = f.read().strip()
                
                # 创建替换脚本
                replacement_script = f"""#!/system/bin/sh
# 公钥替换脚本
# By.举个🌰

echo "开始替换设备公钥..."

# 备份原始公钥
cp /system/etc/security/pub_keys.txt /system/etc/security/pub_keys.txt.backup

# 写入新公钥
echo "{our_pubkey}" > /system/etc/security/pub_keys.txt

echo "公钥替换完成"
echo "现在可以安装自签名的OTA包了"
"""
                
                script_file = Path("replace_pubkey.sh")
                with open(script_file, 'w', encoding='utf-8') as f:
                    f.write(replacement_script)
                
                # 创建ADB命令脚本
                adb_script = f"""@echo off
echo 替换设备公钥脚本
echo By.举个🌰
echo.

echo 连接设备...
adb connect ***************

echo 获取root权限...
adb root

echo 重新挂载系统分区为可写...
adb shell mount -o remount,rw /system

echo 备份原始公钥...
adb shell cp /system/etc/security/pub_keys.txt /system/etc/security/pub_keys.txt.backup

echo 写入新公钥...
adb shell "echo '{our_pubkey}' > /system/etc/security/pub_keys.txt"

echo 验证公钥...
adb shell cat /system/etc/security/pub_keys.txt

echo 重新挂载系统分区为只读...
adb shell mount -o remount,ro /system

echo 公钥替换完成！
echo 现在可以安装自签名的OTA包了
pause
"""
                
                adb_script_file = Path("replace_pubkey.bat")
                with open(adb_script_file, 'w', encoding='utf-8') as f:
                    f.write(adb_script)
                
                self.log(f"✅ 替换脚本已生成:")
                self.log(f"  - {script_file}")
                self.log(f"  - {adb_script_file}")
                
                return True
            else:
                self.log("❌ 找不到生成的公钥文件")
                return False
                
        except Exception as e:
            self.log(f"❌ 生成替换脚本失败: {str(e)}")
            return False
    
    def create_solution_guide(self):
        """创建解决方案指南"""
        self.log("📖 创建解决方案指南...")
        
        guide_content = """# Error 21 解决方案指南

**问题**: Android OTA 签名验证失败 (Error 21)
**解决者**: By.举个🌰

## 🎯 解决方案概述

Error 21 是Android设备在验证OTA包签名时出现的错误。以下提供多种解决方案：

### 方案1: 使用无签名包 + 替换公钥 (推荐)

1. **使用生成的无签名包**:
   - `bypass_official_signed_update_diy.zip`

2. **替换设备公钥**:
   - 运行 `replace_pubkey.bat`
   - 或手动执行ADB命令

3. **安装无签名包**:
   - 设备现在会接受我们的自签名包

### 方案2: 禁用签名验证

如果有Recovery源码或可修改Recovery:
```bash
# 在Recovery中跳过签名验证
# 修改 recovery/verifier.cpp
# 注释掉签名验证相关代码
```

### 方案3: 使用原厂工具

联系设备制造商获取:
- 官方刷机工具
- 原厂签名证书
- 技术支持

## 🔧 详细操作步骤

### 步骤1: 替换设备公钥

```bash
# 连接设备
adb connect ***************

# 获取root权限
adb root

# 重新挂载系统分区
adb shell mount -o remount,rw /system

# 备份原始公钥
adb shell cp /system/etc/security/pub_keys.txt /system/etc/security/pub_keys.txt.backup

# 写入新公钥 (使用我们生成的公钥)
adb shell "echo '你的公钥内容' > /system/etc/security/pub_keys.txt"

# 重新挂载为只读
adb shell mount -o remount,ro /system
```

### 步骤2: 安装无签名包

现在可以安装 `bypass_official_signed_update_diy.zip`

## ⚠️ 注意事项

1. **备份重要数据**: 操作前务必备份
2. **测试环境**: 先在测试设备上验证
3. **恢复方案**: 准备原厂固件用于恢复
4. **设备兼容性**: 确保固件适配设备型号

## 🆘 故障排除

如果仍然失败:
1. 检查设备是否真正获得root权限
2. 确认系统分区是否成功重新挂载
3. 验证公钥是否正确写入
4. 尝试重启设备后再次安装

## 📞 技术支持

- 工具作者: By.举个🌰
- 创建时间: 2025年
- 适用设备: 晶晨机顶盒 CM311-1-ALL

---

**祝你刷机成功！🎉**
"""
        
        guide_file = Path("Error21解决方案指南.md")
        with open(guide_file, 'w', encoding='utf-8') as f:
            f.write(guide_content)
        
        self.log(f"✅ 解决方案指南已创建: {guide_file}")

def main():
    """主函数"""
    print("🔧 签名验证绕过工具")
    print("专门解决 Error 21 问题")
    print("Copyright: By.举个🌰")
    print("-" * 50)
    
    # 创建绕过工具实例
    bypass_tool = SignatureBypassTool()
    
    # 查找签名文件
    signed_files = list(Path(".").glob("*signed_update_diy.zip"))
    
    if not signed_files:
        print("❌ 找不到签名的卡刷包文件")
        return
    
    success_count = 0
    
    # 为每个签名文件创建绕过版本
    for signed_file in signed_files:
        print(f"\n🔄 处理文件: {signed_file}")
        if bypass_tool.create_bypass_package(str(signed_file)):
            success_count += 1
    
    if success_count > 0:
        print(f"\n🎉 成功创建 {success_count} 个绕过验证包")
        
        # 生成替换公钥脚本
        bypass_tool.generate_replacement_keys()
        
        # 创建解决方案指南
        bypass_tool.create_solution_guide()
        
        print("\n📋 生成的文件:")
        for file_path in Path(".").glob("bypass_*.zip"):
            if file_path.exists():
                print(f"  📦 {file_path.name} - 无签名卡刷包")
        
        if Path("replace_pubkey.bat").exists():
            print(f"  🔑 replace_pubkey.bat - 公钥替换脚本")
        
        if Path("Error21解决方案指南.md").exists():
            print(f"  📖 Error21解决方案指南.md - 详细说明")
        
        print("\n🚀 使用步骤:")
        print("1. 运行 replace_pubkey.bat 替换设备公钥")
        print("2. 使用 bypass_*.zip 文件进行刷机")
        print("3. 参考解决方案指南了解详细信息")
        
    else:
        print("\n❌ 创建绕过包失败")

if __name__ == "__main__":
    main()
