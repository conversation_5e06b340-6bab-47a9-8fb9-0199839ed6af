<?xml version="1.0" encoding="UTF-8"?>
<project name="powermanager">
    <module name="auto">
        <component name="/standby_auto/service/key_timeout">
            <intent name="tips_content">您的机顶盒长期未操作，将进入休眠模式节约用电…</intent>
            <intent name="tips_time_content">倒计时$count_down_time$</intent>
            <intent name="use_countdown">true</intent>
            <intent name="use_viewing_time">true</intent>
            <intent name="countdown_time">60</intent>
            <intent name="operator">cancel</intent>
            <intent name="standby_model">shutdown</intent>
        </component>

        <component name="/standby_auto/service/cec_hdmi">
            <intent name="tips_content"></intent>
            <intent name="tips_time_content"></intent>
            <intent name="use_countdown"></intent>
            <intent name="use_viewing_time"></intent>
            <intent name="operator"></intent>
            <intent name="standby_model">shutdown</intent>
        </component>
    </module>

    <module name="key">
        <component name="/standby_key/service/power_key_shutdown">
            <intent name="tips_content"></intent>
            <intent name="tips_time_content"></intent>
            <intent name="use_countdown"></intent>
            <intent name="use_viewing_time"></intent>
            <intent name="operator"></intent>
            <intent name="standby_model">shutdown</intent>
            <intent name="send_action">shutdown</intent>
        </component>
    </module>
</project>
