EXT4 superblock info:

Filesystem volume name:    system
Last mounted on:           /system
Filesystem UUID:           A028C002-572D-5BA7-88E2-237D72A02224
Filesystem magic number:   0xEF53
Filesystem revision:       1 (v2 format/dynamic inode sizes)
FS compatible features:    0x00000038 (EXT_ATTR RESIZE_INODE DIR_INDEX)
FS incompatible features:  0x000002C2 (FILETYPE EXTENTS 64BIT FLEX_BG)
FS read-only features:     0x0000006B (SPARSE_SUPER LARGE_FILE HUGE_FILE DIR_NLINK EXTRA_ISIZE)
Filesystem flags:          Signed directory hash in use
Default mount options:     0x0000000C (XATTR_USER ACL)
Filesystem state:          none
Errors behavior:           Continue
Filesystem OS type:        Linux
Inode count:               81920
Block count:               327680
Reserved block count:      0
Free blocks:               120914
Free inodes:               79328
First data block:          0
Block size:                4096
Fragment size:             4096
Reserved GDT blocks:       159
Blocks per group:          32768
Fragments per group:       32768
Inodes per group:          8192
Inode blocks per group:    512
Last mount time:           22.07.2025 11:29:43
Last write time:           01.01.2009 0:00:00
Mount count:               1
Maximum mount count:       -1
Last checked:              01.01.2009 0:00:00
Check interval:            0
Reserved blocks uid:       0 (user unknown)
Reserved blocks gid:       0 (group unknown)
First inode:               11
Inode size:                256
Journal superblock UUID:   00000000-0000-0000-0000-000000000000
Journal inode:             0
Journal device number:     0x00000000
Default directory hash:    Half MD4.
Directory Hash Seed:       574E027B-**************-91E4CC5050C4
Journal inode backup type: 0x00none

Output information about the superblock of system.img finish success
