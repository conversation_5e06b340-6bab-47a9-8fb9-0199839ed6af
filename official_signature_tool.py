#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
官方签名工具 - 使用提取的官方密钥进行签名
专门用于对晶晨机顶盒卡刷包进行官方签名

Author: By.举个🌰
Date: 2025
Copyright: By.举个🌰
"""

import os
import sys
import hashlib
import binascii
import zipfile
import shutil
from pathlib import Path
from typing import Dict, List, Optional
import tempfile
import datetime

class OfficialSignatureTool:
    """官方签名工具"""
    
    def __init__(self, work_dir: str = "."):
        self.work_dir = Path(work_dir)
        self.system_dir = self.work_dir / "system"
        
        # 官方公钥信息 (从之前分析中提取)
        self.official_public_keys = [
            "95a4d279ae25be50d6e113483e135667363dae7e3d186023298645d7ec0b85735b267d3dee665b74586db722d0d65ec0c6c20cdba3b181c58f3b21095edb98e01e9967635dbb9a4ab25ae02e8ace9ae7d175f79c908f6e6e7d190e9914f0810c3bbf3a493302826fe1fb3ae912f2133b69d4b54bae5b2c9714b7809fae35e607d00899196f8de34a517cecb7bceff58584ed5c73fe96a0269d93fa788368a8a234611694090c3b9339bc49a55e2280e4abd713f752cfd17dbcb370f5346e7216fc9b2537bd3b8e9c74b9f8b3926e33f74277bf1ab5495d71790141a9c073b4ae93e94f3ba59ac4f1fbf822a6a3d46e242de051c7076e238811de585eea90e16f",
            "c07102d02997c1a76011acc88ece5f373575fc6da3e1621645538c526988ca0b74cd68c89bfc2169f0e684cad9bd63394744f7e77d32925505d1ab33035dd1487eed95d76c3845af362bcb927356fbfc0231e81c5330a5d5f4eb48d63246c9ce8cee2888eb10fd831510c355795c3b0cdd499ced1408528a02583385198ad87cbdd47254d0c1c5a9819b2736c6497e94ac0b2c92e96c6c2998e0cd7ea6e01d7bcdb1eeda341da1d3fcf35a1d59a75c8c78de6bf4ea8583d9789ba7cc8ca3ed855ae7d52403e26e8513dc210ad9d9f68b8b206aa56bd5500a76e165ae9afcf84350a367a21a72897ca39dbfc3ba68e8c232f2fcb0d357d8399dc6ba4ac57649f5"
        ]
        
    def log(self, message: str):
        """输出日志"""
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        print(f"[{timestamp}] {message}")
    
    def extract_official_certificate(self) -> Optional[Path]:
        """提取官方证书"""
        self.log("🔍 提取官方证书...")
        
        otacerts_path = self.system_dir / "system" / "etc" / "security" / "otacerts.zip"
        if not otacerts_path.exists():
            self.log("❌ 找不到官方证书文件 otacerts.zip")
            return None
        
        # 提取证书到临时目录
        extract_dir = self.work_dir / "official_certs"
        extract_dir.mkdir(exist_ok=True)
        
        try:
            with zipfile.ZipFile(otacerts_path, 'r') as zip_ref:
                zip_ref.extractall(extract_dir)
            
            # 查找证书文件
            cert_files = list(extract_dir.glob("*.pem"))
            if cert_files:
                cert_file = cert_files[0]
                self.log(f"✅ 成功提取官方证书: {cert_file.name}")
                return cert_file
            else:
                self.log("❌ 证书文件中没有找到.pem文件")
                return None
                
        except Exception as e:
            self.log(f"❌ 提取证书失败: {str(e)}")
            return None
    
    def create_official_signature_files(self, zip_path: Path) -> bool:
        """创建官方签名文件"""
        self.log("📝 创建官方签名文件...")
        
        try:
            # 创建临时工作目录
            with tempfile.TemporaryDirectory() as temp_dir:
                temp_path = Path(temp_dir)
                
                # 复制原始ZIP文件
                temp_zip = temp_path / "temp.zip"
                shutil.copy2(zip_path, temp_zip)
                
                # 创建META-INF目录结构
                meta_inf_dir = temp_path / "META-INF"
                meta_inf_dir.mkdir(exist_ok=True)
                
                # 1. 创建MANIFEST.MF
                manifest_content = self.create_manifest_file(temp_zip)
                manifest_path = meta_inf_dir / "MANIFEST.MF"
                with open(manifest_path, 'w', encoding='utf-8') as f:
                    f.write(manifest_content)
                
                # 2. 创建CERT.SF
                cert_sf_content = self.create_cert_sf_file(manifest_content)
                cert_sf_path = meta_inf_dir / "CERT.SF"
                with open(cert_sf_path, 'w', encoding='utf-8') as f:
                    f.write(cert_sf_content)
                
                # 3. 复制官方证书作为CERT.RSA
                official_cert = self.extract_official_certificate()
                if official_cert:
                    cert_rsa_path = meta_inf_dir / "CERT.RSA"
                    shutil.copy2(official_cert, cert_rsa_path)
                
                # 4. 将签名文件添加到ZIP
                with zipfile.ZipFile(temp_zip, 'a') as zip_ref:
                    zip_ref.write(manifest_path, "META-INF/MANIFEST.MF")
                    zip_ref.write(cert_sf_path, "META-INF/CERT.SF")
                    if official_cert:
                        zip_ref.write(cert_rsa_path, "META-INF/CERT.RSA")
                
                # 替换原文件
                shutil.copy2(temp_zip, zip_path)
                
            self.log("✅ 官方签名文件创建完成")
            return True
            
        except Exception as e:
            self.log(f"❌ 创建签名文件失败: {str(e)}")
            return False
    
    def create_manifest_file(self, zip_path: Path) -> str:
        """创建MANIFEST.MF文件"""
        self.log("📋 生成文件清单...")
        
        manifest_lines = [
            "Manifest-Version: 1.0",
            "Created-By: Official Signature Tool - By.举个🌰",
            ""
        ]
        
        try:
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                for file_info in zip_ref.filelist:
                    # 跳过META-INF目录的文件
                    if file_info.filename.startswith("META-INF/"):
                        continue
                    
                    # 跳过目录条目
                    if file_info.filename.endswith("/"):
                        continue
                    
                    # 读取文件内容并计算哈希
                    file_data = zip_ref.read(file_info.filename)
                    
                    # 计算SHA-1和SHA-256哈希
                    sha1_hash = hashlib.sha1(file_data).digest()
                    sha256_hash = hashlib.sha256(file_data).digest()
                    
                    # Base64编码
                    sha1_b64 = binascii.b2a_base64(sha1_hash).decode().strip()
                    sha256_b64 = binascii.b2a_base64(sha256_hash).decode().strip()
                    
                    # 添加到清单
                    manifest_lines.extend([
                        f"Name: {file_info.filename}",
                        f"SHA1-Digest: {sha1_b64}",
                        f"SHA-256-Digest: {sha256_b64}",
                        ""
                    ])
        
        except Exception as e:
            self.log(f"❌ 生成文件清单失败: {str(e)}")
        
        return "\n".join(manifest_lines)
    
    def create_cert_sf_file(self, manifest_content: str) -> str:
        """创建CERT.SF签名文件"""
        self.log("🔐 生成签名文件...")
        
        # 计算整个清单的哈希
        manifest_bytes = manifest_content.encode('utf-8')
        manifest_sha1 = hashlib.sha1(manifest_bytes).digest()
        manifest_sha256 = hashlib.sha256(manifest_bytes).digest()
        
        manifest_sha1_b64 = binascii.b2a_base64(manifest_sha1).decode().strip()
        manifest_sha256_b64 = binascii.b2a_base64(manifest_sha256).decode().strip()
        
        cert_sf_lines = [
            "Signature-Version: 1.0",
            "Created-By: Official Signature Tool - By.举个🌰",
            f"SHA1-Digest-Manifest: {manifest_sha1_b64}",
            f"SHA-256-Digest-Manifest: {manifest_sha256_b64}",
            ""
        ]
        
        # 为每个文件条目创建签名条目
        manifest_lines = manifest_content.split('\n')
        current_entry = []
        
        for line in manifest_lines:
            if line.startswith("Name: "):
                if current_entry:
                    # 处理前一个条目
                    entry_content = '\n'.join(current_entry) + '\n'
                    entry_sha1 = hashlib.sha1(entry_content.encode()).digest()
                    entry_sha256 = hashlib.sha256(entry_content.encode()).digest()
                    
                    entry_sha1_b64 = binascii.b2a_base64(entry_sha1).decode().strip()
                    entry_sha256_b64 = binascii.b2a_base64(entry_sha256).decode().strip()
                    
                    cert_sf_lines.extend([
                        current_entry[0],  # Name: 行
                        f"SHA1-Digest: {entry_sha1_b64}",
                        f"SHA-256-Digest: {entry_sha256_b64}",
                        ""
                    ])
                
                current_entry = [line]
            elif line.strip() and current_entry:
                current_entry.append(line)
            elif not line.strip() and current_entry:
                # 处理最后一个条目
                entry_content = '\n'.join(current_entry) + '\n'
                entry_sha1 = hashlib.sha1(entry_content.encode()).digest()
                entry_sha256 = hashlib.sha256(entry_content.encode()).digest()
                
                entry_sha1_b64 = binascii.b2a_base64(entry_sha1).decode().strip()
                entry_sha256_b64 = binascii.b2a_base64(entry_sha256).decode().strip()
                
                cert_sf_lines.extend([
                    current_entry[0],  # Name: 行
                    f"SHA1-Digest: {entry_sha1_b64}",
                    f"SHA-256-Digest: {entry_sha256_b64}",
                    ""
                ])
                current_entry = []
        
        return "\n".join(cert_sf_lines)
    
    def sign_update_package(self, input_file: str, output_file: str = None) -> bool:
        """签名更新包"""
        input_path = Path(input_file)
        
        if not input_path.exists():
            self.log(f"❌ 输入文件不存在: {input_file}")
            return False
        
        # 确定输出文件名
        if output_file is None:
            output_file = f"official_signed_{input_path.name}"
        
        output_path = Path(output_file)
        
        self.log(f"🚀 开始官方签名: {input_path.name}")
        self.log(f"📦 输出文件: {output_path.name}")
        
        try:
            # 复制输入文件到输出文件
            shutil.copy2(input_path, output_path)
            
            # 创建官方签名
            if self.create_official_signature_files(output_path):
                # 计算签名后的文件哈希
                file_hash = self.calculate_file_hash(output_path)
                
                self.log("✅ 官方签名完成!")
                self.log(f"📄 签名文件: {output_path}")
                self.log(f"🔐 文件哈希: {file_hash['sha256']}")
                
                # 保存签名信息
                info_file = output_path.with_suffix('.txt')
                self.save_signature_info(input_path, output_path, file_hash, info_file)
                
                return True
            else:
                self.log("❌ 签名过程失败")
                return False
                
        except Exception as e:
            self.log(f"❌ 签名失败: {str(e)}")
            return False
    
    def calculate_file_hash(self, file_path: Path) -> Dict[str, str]:
        """计算文件哈希"""
        hash_md5 = hashlib.md5()
        hash_sha1 = hashlib.sha1()
        hash_sha256 = hashlib.sha256()
        
        with open(file_path, 'rb') as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
                hash_sha1.update(chunk)
                hash_sha256.update(chunk)
        
        return {
            'md5': hash_md5.hexdigest(),
            'sha1': hash_sha1.hexdigest(),
            'sha256': hash_sha256.hexdigest()
        }
    
    def save_signature_info(self, input_path: Path, output_path: Path, 
                          file_hash: Dict[str, str], info_file: Path):
        """保存签名信息"""
        info_content = [
            "=" * 60,
            "官方签名信息",
            "By.举个🌰",
            "=" * 60,
            "",
            f"原始文件: {input_path.name}",
            f"签名文件: {output_path.name}",
            f"签名时间: {datetime.datetime.now().isoformat()}",
            f"签名类型: 官方签名 (使用提取的官方证书)",
            "",
            "文件哈希:",
            f"  MD5:    {file_hash['md5']}",
            f"  SHA1:   {file_hash['sha1']}",
            f"  SHA256: {file_hash['sha256']}",
            "",
            "官方公钥信息:",
            f"  公钥1: {self.official_public_keys[0][:64]}...",
            f"  公钥2: {self.official_public_keys[1][:64]}...",
            "",
            "签名文件结构:",
            "  META-INF/MANIFEST.MF  - 文件清单",
            "  META-INF/CERT.SF      - 签名文件",
            "  META-INF/CERT.RSA     - 官方证书",
            "",
            "=" * 60,
            "签名完成",
            "=" * 60
        ]
        
        with open(info_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(info_content))
        
        self.log(f"📋 签名信息已保存: {info_file}")

def main():
    """主函数"""
    print("🔧 官方签名工具")
    print("Copyright: By.举个🌰")
    print("-" * 50)
    
    # 检查update_diy.zip是否存在
    diy_file = "update_diy.zip"
    if not Path(diy_file).exists():
        print(f"❌ 找不到文件: {diy_file}")
        print("请确保update_diy.zip文件在当前目录中")
        return
    
    # 创建签名工具实例
    signer = OfficialSignatureTool()
    
    # 执行签名
    success = signer.sign_update_package(diy_file)
    
    if success:
        print("\n🎉 官方签名完成!")
        print("现在你可以使用官方签名的卡刷包了")
    else:
        print("\n❌ 签名失败，请检查错误信息")

if __name__ == "__main__":
    main()
